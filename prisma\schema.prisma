// CAMPMAKER - Sistema de Gestão de Campeonatos de E-sports
// Schema do Prisma para PostgreSQL

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ================================
// SISTEMA DE USUÁRIOS
// ================================

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  type      UserType
  avatar    String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relacionamentos
  playerProfile        PlayerProfile?
  organizedTournaments Tournament[]
  tournamentRegistrations TournamentRegistration[]
  teamMemberships      TeamMember[]
  captainedTeams       Team[]       @relation("TeamCaptain")
  achievements         Achievement[]
  notifications        Notification[]

  @@map("users")
}

model PlayerProfile {
  userId         String   @id @map("user_id")
  age            Int?
  peripherals    String?
  preferredGames String[] @map("preferred_games")
  rank           String?
  twitchUrl      String?  @map("twitch_url")
  youtubeUrl     String?  @map("youtube_url")
  discordTag     String?  @map("discord_tag")
  teamId         String?  @map("team_id")
  createdAt      DateTime @default(now()) @map("created_at")

  // Relacionamentos
  user User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  team Team? @relation(fields: [teamId], references: [id])

  @@map("player_profiles")
}

// ================================
// SISTEMA DE TIMES
// ================================

model Team {
  id        String   @id @default(cuid())
  name      String
  logo      String?
  captainId String   @map("captain_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relacionamentos
  captain              User                     @relation("TeamCaptain", fields: [captainId], references: [id])
  members              TeamMember[]
  playerProfiles       PlayerProfile[]
  tournamentRegistrations TournamentRegistration[]
  achievements         Achievement[]

  @@map("teams")
}

model TeamMember {
  id       String   @id @default(cuid())
  teamId   String   @map("team_id")
  playerId String   @map("player_id")
  joinedAt DateTime @default(now()) @map("joined_at")

  // Relacionamentos
  team   Team @relation(fields: [teamId], references: [id], onDelete: Cascade)
  player User @relation(fields: [playerId], references: [id], onDelete: Cascade)

  @@unique([teamId, playerId])
  @@map("team_members")
}

// ================================
// SISTEMA DE TORNEIOS
// ================================

model Tournament {
  id                   String            @id @default(cuid())
  name                 String
  game                 String
  organizerId          String            @map("organizer_id")
  description          String?
  format               TournamentFormat
  maxParticipants      Int               @map("max_participants")
  registrationFee      Decimal           @default(0) @map("registration_fee") @db.Decimal(10, 2)
  prizePool            Decimal           @default(0) @map("prize_pool") @db.Decimal(10, 2)
  rules                String[]
  platforms            String[]
  startDate            DateTime          @map("start_date")
  endDate              DateTime          @map("end_date")
  registrationDeadline DateTime          @map("registration_deadline")
  status               TournamentStatus  @default(UPCOMING)
  createdAt            DateTime          @default(now()) @map("created_at")
  updatedAt            DateTime          @updatedAt @map("updated_at")

  // Relacionamentos
  organizer            User                     @relation(fields: [organizerId], references: [id])
  prizeDistributions   PrizeDistribution[]
  registrations        TournamentRegistration[]
  bracketMatches       BracketMatch[]
  achievements         Achievement[]

  @@map("tournaments")
}

model PrizeDistribution {
  id           String @id @default(cuid())
  tournamentId String @map("tournament_id")
  place        Int
  percentage   Decimal @db.Decimal(5, 2)

  // Relacionamentos
  tournament Tournament @relation(fields: [tournamentId], references: [id], onDelete: Cascade)

  @@unique([tournamentId, place])
  @@map("prize_distributions")
}

model TournamentRegistration {
  id              String            @id @default(cuid())
  tournamentId    String            @map("tournament_id")
  participantId   String            @map("participant_id")
  participantType ParticipantType   @map("participant_type")
  registeredAt    DateTime          @default(now()) @map("registered_at")

  // Relacionamentos
  tournament Tournament @relation(fields: [tournamentId], references: [id], onDelete: Cascade)
  user       User?      @relation(fields: [participantId], references: [id], onDelete: Cascade, map: "tournament_registrations_user_fkey")
  team       Team?      @relation(fields: [participantId], references: [id], onDelete: Cascade, map: "tournament_registrations_team_fkey")

  @@unique([tournamentId, participantId])
  @@map("tournament_registrations")
}

// ================================
// SISTEMA DE BRACKETS/CHAVES
// ================================

model BracketMatch {
  id           String      @id @default(cuid())
  tournamentId String      @map("tournament_id")
  round        Int
  position     Int
  participant1 String?     @map("participant1")
  participant2 String?     @map("participant2")
  winner       String?
  scoreP1      Int?        @map("score_p1")
  scoreP2      Int?        @map("score_p2")
  status       MatchStatus @default(PENDING)
  scheduledAt  DateTime?   @map("scheduled_at")
  createdAt    DateTime    @default(now()) @map("created_at")

  // Relacionamentos
  tournament Tournament @relation(fields: [tournamentId], references: [id], onDelete: Cascade)

  @@map("bracket_matches")
}

// ================================
// SISTEMA DE CONQUISTAS
// ================================

model Achievement {
  id             String   @id @default(cuid())
  tournamentId   String   @map("tournament_id")
  tournamentName String   @map("tournament_name")
  userId         String?  @map("user_id")
  teamId         String?  @map("team_id")
  place          Int
  prize          Decimal? @db.Decimal(10, 2)
  date           DateTime

  // Relacionamentos
  tournament Tournament @relation(fields: [tournamentId], references: [id], onDelete: Cascade)
  user       User?      @relation(fields: [userId], references: [id], onDelete: Cascade)
  team       Team?      @relation(fields: [teamId], references: [id], onDelete: Cascade)

  @@map("achievements")
}

// ================================
// SISTEMA DE NOTIFICAÇÕES
// ================================

model Notification {
  id        String           @id @default(cuid())
  userId    String           @map("user_id")
  type      NotificationType
  title     String
  message   String
  read      Boolean          @default(false)
  createdAt DateTime         @default(now()) @map("created_at")

  // Relacionamentos
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// ================================
// ENUMS
// ================================

enum UserType {
  PLAYER     @map("player")
  ORGANIZER  @map("organizer")
}

enum TournamentFormat {
  SINGLE_ELIMINATION @map("single-elimination")
  DOUBLE_ELIMINATION @map("double-elimination")
  GROUPS             @map("groups")
  SWISS              @map("swiss")
}

enum TournamentStatus {
  UPCOMING     @map("upcoming")
  REGISTRATION @map("registration")
  ONGOING      @map("ongoing")
  COMPLETED    @map("completed")
  CANCELLED    @map("cancelled")
}

enum ParticipantType {
  PLAYER @map("player")
  TEAM   @map("team")
}

enum MatchStatus {
  PENDING   @map("pending")
  ONGOING   @map("ongoing")
  COMPLETED @map("completed")
}

enum NotificationType {
  TOURNAMENT @map("tournament")
  MATCH      @map("match")
  SYSTEM     @map("system")
}
