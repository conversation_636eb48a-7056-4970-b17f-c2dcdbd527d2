// Script para testar o banco de dados
// Execute com: node scripts/test-db.js

import { testDatabase, cleanTestData } from '../src/lib/test-db.js';

async function main() {
  console.log('🚀 Iniciando testes do banco de dados...\n');
  
  const result = await testDatabase();
  
  if (result.success) {
    console.log('\n🎉 SUCESSO! Banco de dados funcionando perfeitamente!');
    console.log(`📊 Estatísticas:`);
    console.log(`   - Usuários: ${result.users}`);
    console.log(`   - Torneios: ${result.tournaments}`);
    
    // Limpar dados de teste
    console.log('\n🧹 Limpando dados de teste...');
    await cleanTestData();
    
  } else {
    console.log('\n❌ ERRO! Problemas encontrados:');
    console.log(`   ${result.error}`);
    process.exit(1);
  }
}

main().catch(console.error);
