// Serviço para comunicação com a API real
const API_BASE_URL = 'http://localhost:5000/api';

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

class ApiService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('authToken');
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    const data = await response.json();

    if (!response.ok) {
      // Retornar objeto com erro em vez de throw para melhor UX
      return {
        success: false,
        error: data.error || `HTTP error! status: ${response.status}`
      } as T;
    }

    return data;
  }

  // AUTH ENDPOINTS
  async login(email: string, password: string, type: string) {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ email, password, type })
    });
    
    return this.handleResponse(response);
  }

  async register(email: string, password: string, name: string, type: string) {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ email, password, name, type })
    });
    
    return this.handleResponse(response);
  }

  async getMe() {
    const response = await fetch(`${API_BASE_URL}/auth/me`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  // TOURNAMENT ENDPOINTS
  async getTournaments() {
    const response = await fetch(`${API_BASE_URL}/tournaments`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  async createTournament(tournamentData: any) {
    const response = await fetch(`${API_BASE_URL}/tournaments`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(tournamentData)
    });
    
    return this.handleResponse(response);
  }

  async registerForTournament(tournamentId: string, registrationData?: { participantType?: string; teamId?: string }) {
    const response = await fetch(`${API_BASE_URL}/tournaments/${tournamentId}/register`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(registrationData || {})
    });

    return this.handleResponse(response);
  }

  async getMyTournaments() {
    const response = await fetch(`${API_BASE_URL}/tournaments/my`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });
    
    return this.handleResponse(response);
  }

  // USER ENDPOINTS
  async getUserStats() {
    const response = await fetch(`${API_BASE_URL}/users/stats`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    return this.handleResponse(response);
  }

  async getUserProfile() {
    const response = await fetch(`${API_BASE_URL}/users/profile`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    return this.handleResponse(response);
  }

  async updateUserProfile(profileData: {
    company?: string;
    website?: string;
    socialMedia?: any;
    bio?: string;
  }) {
    const response = await fetch(`${API_BASE_URL}/users/profile`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(profileData)
    });

    return this.handleResponse(response);
  }

  async searchUserByEmail(email: string) {
    const response = await fetch(`${API_BASE_URL}/users/search?email=${encodeURIComponent(email)}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    return this.handleResponse(response);
  }

  // TEAM ENDPOINTS
  async getTeamsByGame(game: string) {
    const response = await fetch(`${API_BASE_URL}/teams?game=${encodeURIComponent(game)}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    return this.handleResponse(response);
  }

  async createTeam(teamData: { name: string; game: string; logo?: string }) {
    const response = await fetch(`${API_BASE_URL}/teams`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(teamData)
    });

    return this.handleResponse(response);
  }

  async getMyTeams() {
    const response = await fetch(`${API_BASE_URL}/teams/my`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    return this.handleResponse(response);
  }

  async addPlayerToTeam(teamId: string, playerId: string) {
    const response = await fetch(`${API_BASE_URL}/teams/${teamId}/members`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ playerId })
    });

    return this.handleResponse(response);
  }

  async removePlayerFromTeam(teamId: string, playerId: string) {
    const response = await fetch(`${API_BASE_URL}/teams/${teamId}/members/${playerId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    return this.handleResponse(response);
  }

  async checkPlayerTeamForGame(game: string) {
    const response = await fetch(`${API_BASE_URL}/teams/check/${encodeURIComponent(game)}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    return this.handleResponse(response);
  }

  // TEAM INVITE ENDPOINTS
  async sendTeamInvite(teamId: string, playerId: string, message?: string) {
    const response = await fetch(`${API_BASE_URL}/teams/${teamId}/invite`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ playerId, message })
    });

    return this.handleResponse(response);
  }

  async getReceivedInvites() {
    const response = await fetch(`${API_BASE_URL}/teams/invites/received`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    return this.handleResponse(response);
  }

  async getSentInvites(teamId: string) {
    const response = await fetch(`${API_BASE_URL}/teams/${teamId}/invites`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    return this.handleResponse(response);
  }

  async respondToInvite(inviteId: string, response: 'ACCEPTED' | 'DECLINED') {
    const apiResponse = await fetch(`${API_BASE_URL}/teams/invites/${inviteId}/respond`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ response })
    });

    return this.handleResponse(apiResponse);
  }

  async cancelInvite(inviteId: string) {
    const response = await fetch(`${API_BASE_URL}/teams/invites/${inviteId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders()
    });

    return this.handleResponse(response);
  }
}

export const apiService = new ApiService();
