import React from 'react';
import { 
  Trophy, 
  Target, 
  Users, 
  Calendar, 
  TrendingUp, 
  Medal,
  GamepadIcon,
  Search
} from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { useAuth } from '../../contexts/AuthContext';

interface PlayerDashboardProps {
  onViewChange: (view: string) => void;
}

export function PlayerDashboard({ onViewChange }: PlayerDashboardProps) {
  const { tournaments } = useApp();
  const { user } = useAuth();
  
  const registeredTournaments = tournaments.filter(t => t.participants.includes(user?.id || ''));
  const availableTournaments = tournaments.filter(t => 
    t.status === 'registration' && 
    !t.participants.includes(user?.id || '') &&
    t.participants.length < t.maxParticipants
  );

  const stats = [
    {
      title: 'Torneios Inscritos',
      value: registeredTournaments.length,
      icon: Trophy,
      color: 'from-purple-500 to-blue-500',
      change: '+3'
    },
    {
      title: 'Posi<PERSON> Média',
      value: '4º',
      icon: Target,
      color: 'from-green-500 to-teal-500',
      change: '+2'
    },
    {
      title: 'Vitórias',
      value: '12',
      icon: Medal,
      color: 'from-yellow-500 to-orange-500',
      change: '+4'
    },
    {
      title: 'Jogos',
      value: '5',
      icon: GamepadIcon,
      color: 'from-pink-500 to-red-500',
      change: '+1'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 rounded-2xl p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Olá, {user?.name}!</h1>
            <p className="text-green-100 text-lg">
              Encontre novos torneios e acompanhe seu progresso
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center">
              <GamepadIcon className="w-10 h-10 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="bg-gray-800 rounded-xl p-6 border border-gray-700 hover:border-gray-600 transition-all duration-200">
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <div className="flex items-center space-x-1 text-green-400 text-sm">
                  <TrendingUp className="w-4 h-4" />
                  <span>{stat.change}</span>
                </div>
              </div>
              <h3 className="text-2xl font-bold text-white mb-1">{stat.value}</h3>
              <p className="text-gray-400 text-sm">{stat.title}</p>
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <button
          onClick={() => onViewChange('tournaments')}
          className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl p-6 text-white hover:from-purple-700 hover:to-blue-700 transition-all duration-200 text-left group"
        >
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
              <Search className="w-6 h-6" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Buscar Torneios</h3>
              <p className="text-purple-100 text-sm">Encontre novos campeonatos</p>
            </div>
          </div>
        </button>

        <button
          onClick={() => onViewChange('teams')}
          className="bg-gradient-to-r from-green-600 to-teal-600 rounded-xl p-6 text-white hover:from-green-700 hover:to-teal-700 transition-all duration-200 text-left group"
        >
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
              <Users className="w-6 h-6" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Encontrar Time</h3>
              <p className="text-green-100 text-sm">Conecte-se com outros jogadores</p>
            </div>
          </div>
        </button>

        <button
          onClick={() => onViewChange('profile')}
          className="bg-gradient-to-r from-yellow-600 to-orange-600 rounded-xl p-6 text-white hover:from-yellow-700 hover:to-orange-700 transition-all duration-200 text-left group"
        >
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
              <Target className="w-6 h-6" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Meu Perfil</h3>
              <p className="text-yellow-100 text-sm">Atualize suas informações</p>
            </div>
          </div>
        </button>
      </div>

      {/* Available Tournaments */}
      <div className="bg-gray-800 rounded-xl border border-gray-700">
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-white">Torneios Disponíveis</h2>
            <button
              onClick={() => onViewChange('tournaments')}
              className="text-purple-400 hover:text-purple-300 font-medium transition-colors duration-200"
            >
              Ver todos
            </button>
          </div>
        </div>
        <div className="p-6">
          {availableTournaments.length > 0 ? (
            <div className="space-y-4">
              {availableTournaments.slice(0, 3).map((tournament) => (
                <div key={tournament.id} className="flex items-center justify-between p-4 bg-gray-750 rounded-lg border border-gray-600 hover:border-gray-500 transition-colors duration-200">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                      <Trophy className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium text-white">{tournament.name}</h3>
                      <p className="text-sm text-gray-400">{tournament.game}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-green-400 font-medium">
                      R$ {tournament.prizePool.toLocaleString('pt-BR')}
                    </div>
                    <p className="text-sm text-gray-400">
                      {tournament.participants.length}/{tournament.maxParticipants} vagas
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Trophy className="w-12 h-12 text-gray-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-400 mb-2">Nenhum torneio disponível</h3>
              <p className="text-gray-500">Novos campeonatos aparecem aqui regularmente</p>
            </div>
          )}
        </div>
      </div>

      {/* My Tournaments */}
      <div className="bg-gray-800 rounded-xl border border-gray-700">
        <div className="p-6 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">Meus Torneios</h2>
        </div>
        <div className="p-6">
          {registeredTournaments.length > 0 ? (
            <div className="space-y-4">
              {registeredTournaments.map((tournament) => (
                <div key={tournament.id} className="flex items-center justify-between p-4 bg-gray-750 rounded-lg border border-gray-600">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center">
                      <Medal className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium text-white">{tournament.name}</h3>
                      <p className="text-sm text-gray-400">{tournament.game}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                      tournament.status === 'ongoing' 
                        ? 'bg-green-500/20 text-green-400'
                        : tournament.status === 'registration'
                        ? 'bg-blue-500/20 text-blue-400'
                        : 'bg-yellow-500/20 text-yellow-400'
                    }`}>
                      {tournament.status === 'ongoing' && 'Em andamento'}
                      {tournament.status === 'registration' && 'Aguardando início'}
                      {tournament.status === 'upcoming' && 'Próximo'}
                    </div>
                    <p className="text-sm text-gray-400 mt-1">
                      {new Date(tournament.startDate).toLocaleDateString('pt-BR')}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Medal className="w-12 h-12 text-gray-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-400 mb-2">Nenhum torneio inscrito</h3>
              <p className="text-gray-500 mb-6">Participe de um campeonato para começar</p>
              <button
                onClick={() => onViewChange('tournaments')}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200"
              >
                Buscar Torneios
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}