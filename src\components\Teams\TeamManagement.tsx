import React, { useState, useEffect } from 'react';
import { Users, Plus, GamepadIcon, Crown } from 'lucide-react';
import { TeamList } from './TeamList';
import { CreateTeam } from './CreateTeam';
import { MyTeams } from './MyTeams';
import { useAuth } from '../../contexts/AuthContext';

type TabType = 'browse' | 'my-teams' | 'create';

const GAMES = [
  'Counter-Strike 2',
  'Valorant',
  'League of Legends',
  'Dota 2',
  'Apex Legends',
  'Overwatch 2',
  'Rainbow Six Siege',
  'Rocket League'
];

export function TeamManagement() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>('browse');
  const [selectedGame, setSelectedGame] = useState('Counter-Strike 2');

  const isPlayer = user?.type === 'player';

  if (!isPlayer) {
    return (
      <div className="text-center py-12">
        <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">Acesso Restrito</h3>
        <p className="text-gray-400">
          Apenas jogadores podem gerenciar times.
        </p>
      </div>
    );
  }

  const handleTeamCreated = () => {
    setActiveTab('my-teams');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">Gerenciar Times</h2>
          <p className="text-gray-400">
            Crie times, encontre companheiros de equipe e participe de torneios
          </p>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-800 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('browse')}
          className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'browse'
              ? 'bg-purple-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          <GamepadIcon className="w-4 h-4" />
          <span>Explorar Times</span>
        </button>

        <button
          onClick={() => setActiveTab('my-teams')}
          className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'my-teams'
              ? 'bg-purple-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          <Crown className="w-4 h-4" />
          <span>Meus Times</span>
        </button>

        <button
          onClick={() => setActiveTab('create')}
          className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'create'
              ? 'bg-purple-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          <Plus className="w-4 h-4" />
          <span>Criar Time</span>
        </button>
      </div>

      {/* Game Selector (only for browse tab) */}
      {activeTab === 'browse' && (
        <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">Selecionar Jogo</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {GAMES.map((game) => (
              <button
                key={game}
                onClick={() => setSelectedGame(game)}
                className={`p-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                  selectedGame === game
                    ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {game}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Content */}
      <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        {activeTab === 'browse' && (
          <TeamList selectedGame={selectedGame} />
        )}

        {activeTab === 'my-teams' && (
          <MyTeams />
        )}

        {activeTab === 'create' && (
          <CreateTeam
            onTeamCreated={handleTeamCreated}
            onCancel={() => setActiveTab('browse')}
          />
        )}
      </div>
    </div>
  );
}
