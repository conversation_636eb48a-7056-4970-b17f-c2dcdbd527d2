import React from 'react';
import {
  Trophy,
  Users,
  DollarSign,
  Calendar,
  TrendingUp,
  Clock,
  Award,
  Eye,
  User
} from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { useAuth } from '../../contexts/AuthContext';

interface OrganizerDashboardProps {
  onViewChange: (view: string) => void;
}

export function OrganizerDashboard({ onViewChange }: OrganizerDashboardProps) {
  const { tournaments } = useApp();
  const { user } = useAuth();
  
  const myTournaments = tournaments.filter(t => t.organizerId === user?.id);
  const activeTournaments = myTournaments.filter(t => t.status === 'ongoing' || t.status === 'registration');
  const totalRevenue = myTournaments.reduce((sum, t) => sum + (t.registrationFee * t.participants.length), 0);
  const totalParticipants = myTournaments.reduce((sum, t) => sum + t.participants.length, 0);

  const stats = [
    {
      title: 'Torneios Ativos',
      value: activeTournaments.length,
      icon: Trophy,
      color: 'from-purple-500 to-blue-500',
      change: '+12%'
    },
    {
      title: 'Total de Participantes',
      value: totalParticipants,
      icon: Users,
      color: 'from-green-500 to-teal-500',
      change: '+23%'
    },
    {
      title: 'Receita Total',
      value: `R$ ${totalRevenue.toLocaleString('pt-BR')}`,
      icon: DollarSign,
      color: 'from-yellow-500 to-orange-500',
      change: '+8%'
    },
    {
      title: 'Torneios Criados',
      value: myTournaments.length,
      icon: Award,
      color: 'from-pink-500 to-red-500',
      change: '+4%'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Bem-vindo, {user?.name}!</h1>
            <p className="text-purple-100 text-lg">
              Gerencie seus torneios e acompanhe o desempenho da sua organização
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center">
              <Trophy className="w-10 h-10 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="bg-gray-800 rounded-xl p-6 border border-gray-700 hover:border-gray-600 transition-all duration-200">
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <div className="flex items-center space-x-1 text-green-400 text-sm">
                  <TrendingUp className="w-4 h-4" />
                  <span>{stat.change}</span>
                </div>
              </div>
              <h3 className="text-2xl font-bold text-white mb-1">{stat.value}</h3>
              <p className="text-gray-400 text-sm">{stat.title}</p>
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <button
          onClick={() => onViewChange('create-tournament')}
          className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl p-6 text-white hover:from-purple-700 hover:to-blue-700 transition-all duration-200 text-left group"
        >
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
              <Trophy className="w-6 h-6" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Criar Torneio</h3>
              <p className="text-purple-100 text-sm">Configure um novo campeonato</p>
            </div>
          </div>
        </button>

        <button
          onClick={() => onViewChange('tournaments')}
          className="bg-gradient-to-r from-green-600 to-teal-600 rounded-xl p-6 text-white hover:from-green-700 hover:to-teal-700 transition-all duration-200 text-left group"
        >
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
              <Eye className="w-6 h-6" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Ver Torneios</h3>
              <p className="text-green-100 text-sm">Gerencie seus campeonatos</p>
            </div>
          </div>
        </button>

        <button
          onClick={() => onViewChange('profile')}
          className="bg-gradient-to-r from-orange-600 to-red-600 rounded-xl p-6 text-white hover:from-orange-700 hover:to-red-700 transition-all duration-200 text-left group"
        >
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
              <User className="w-6 h-6" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Meu Perfil</h3>
              <p className="text-orange-100 text-sm">Gerenciar informações</p>
            </div>
          </div>
        </button>
      </div>

      {/* Recent Tournaments */}
      <div className="bg-gray-800 rounded-xl border border-gray-700">
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-white">Torneios Recentes</h2>
            <button
              onClick={() => onViewChange('tournaments')}
              className="text-purple-400 hover:text-purple-300 font-medium transition-colors duration-200"
            >
              Ver todos
            </button>
          </div>
        </div>
        <div className="p-6">
          {myTournaments.length > 0 ? (
            <div className="space-y-4">
              {myTournaments.slice(0, 5).map((tournament) => (
                <div key={tournament.id} className="flex items-center justify-between p-4 bg-gray-750 rounded-lg border border-gray-600 hover:border-gray-500 transition-colors duration-200">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                      <Trophy className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium text-white">{tournament.name}</h3>
                      <p className="text-sm text-gray-400">{tournament.game}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                      tournament.status === 'ongoing' 
                        ? 'bg-green-500/20 text-green-400'
                        : tournament.status === 'registration'
                        ? 'bg-blue-500/20 text-blue-400'
                        : tournament.status === 'completed'
                        ? 'bg-gray-500/20 text-gray-400'
                        : 'bg-yellow-500/20 text-yellow-400'
                    }`}>
                      {tournament.status === 'ongoing' && 'Em andamento'}
                      {tournament.status === 'registration' && 'Inscrições abertas'}
                      {tournament.status === 'completed' && 'Finalizado'}
                      {tournament.status === 'upcoming' && 'Próximo'}
                    </div>
                    <p className="text-sm text-gray-400 mt-1">
                      {tournament.participants.length}/{tournament.maxParticipants} inscritos
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Trophy className="w-12 h-12 text-gray-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-400 mb-2">Nenhum torneio criado</h3>
              <p className="text-gray-500 mb-6">Comece criando seu primeiro campeonato</p>
              <button
                onClick={() => onViewChange('create-tournament')}
                className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200"
              >
                Criar Torneio
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}