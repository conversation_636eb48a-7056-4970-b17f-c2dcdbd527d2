import React, { useState } from 'react';
import { Users, AlertCircle, CheckCircle } from 'lucide-react';
import { apiService } from '../../services/apiService';

interface CreateTeamProps {
  onTeamCreated?: () => void;
  onCancel?: () => void;
}

const GAMES = [
  'Counter-Strike 2',
  'Valorant',
  'League of Legends',
  'Dota 2',
  'Apex Legends',
  'Overwatch 2',
  'Rainbow Six Siege',
  'Rocket League'
];

export function CreateTeam({ onTeamCreated, onCancel }: CreateTeamProps) {
  const [formData, setFormData] = useState({
    name: '',
    game: '',
    logo: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.game) {
      setError('Nome e jogo são obrigatórios');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.createTeam({
        name: formData.name.trim(),
        game: formData.game,
        logo: formData.logo.trim() || undefined
      });

      if (response.success) {
        setSuccess(true);
        setTimeout(() => {
          onTeamCreated?.();
        }, 1500);
      } else {
        setError(response.error || 'Erro ao criar time');
      }
    } catch (err) {
      setError('Erro ao criar time. Tente novamente.');
      console.error('Erro ao criar time:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Limpar erro quando usuário começar a digitar
    if (error) setError(null);
  };

  if (success) {
    return (
      <div className="bg-gray-800 rounded-xl p-8 border border-gray-700">
        <div className="text-center">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">Time Criado!</h3>
          <p className="text-gray-400 mb-4">
            Seu time "{formData.name}" foi criado com sucesso para {formData.game}.
          </p>
          <p className="text-sm text-gray-500">
            Você foi automaticamente adicionado como capitão.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-xl p-8 border border-gray-700">
      <div className="flex items-center space-x-3 mb-6">
        <Users className="w-6 h-6 text-purple-500" />
        <h3 className="text-xl font-semibold text-white">Criar Novo Time</h3>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg flex items-center space-x-3">
          <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
            Nome do Time *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="Ex: Thunder Wolves"
            className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={loading}
            maxLength={50}
          />
          <p className="text-xs text-gray-500 mt-1">
            {formData.name.length}/50 caracteres
          </p>
        </div>

        <div>
          <label htmlFor="game" className="block text-sm font-medium text-gray-300 mb-2">
            Jogo *
          </label>
          <select
            id="game"
            name="game"
            value={formData.game}
            onChange={handleChange}
            className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={loading}
          >
            <option value="">Selecione um jogo</option>
            {GAMES.map((game) => (
              <option key={game} value={game}>
                {game}
              </option>
            ))}
          </select>
          <p className="text-xs text-gray-500 mt-1">
            Você só pode estar em um time por jogo
          </p>
        </div>

        <div>
          <label htmlFor="logo" className="block text-sm font-medium text-gray-300 mb-2">
            Logo do Time (URL)
          </label>
          <input
            type="url"
            id="logo"
            name="logo"
            value={formData.logo}
            onChange={handleChange}
            placeholder="https://exemplo.com/logo.png"
            className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={loading}
          />
          <p className="text-xs text-gray-500 mt-1">
            Opcional - URL da imagem do logo do time
          </p>
        </div>

        <div className="flex space-x-4 pt-4">
          <button
            type="submit"
            disabled={loading || !formData.name.trim() || !formData.game}
            className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            {loading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Criando...</span>
              </div>
            ) : (
              'Criar Time'
            )}
          </button>

          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              disabled={loading}
              className="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 transition-colors"
            >
              Cancelar
            </button>
          )}
        </div>
      </form>

      <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
        <h4 className="text-sm font-medium text-blue-400 mb-2">ℹ️ Informações Importantes:</h4>
        <ul className="text-xs text-blue-300 space-y-1">
          <li>• Você será automaticamente o capitão do time</li>
          <li>• Você só pode estar em um time por jogo</li>
          <li>• Você pode estar em times de jogos diferentes</li>
          <li>• O nome do time deve ser único</li>
        </ul>
      </div>
    </div>
  );
}
