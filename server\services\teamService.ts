import { prisma } from '../lib/prisma';

export class TeamService {
  // Verificar se um jogador já está em um time para um jogo específico
  static async isPlayerInTeamForGame(playerId: string, game: string): Promise<{ inTeam: boolean; teamId?: string; teamName?: string }> {
    try {
      const teamMembership = await prisma.teamMember.findFirst({
        where: {
          playerId,
          team: {
            game: game
          }
        },
        include: {
          team: {
            select: {
              id: true,
              name: true,
              game: true
            }
          }
        }
      });

      if (teamMembership) {
        return {
          inTeam: true,
          teamId: teamMembership.team.id,
          teamName: teamMembership.team.name
        };
      }

      return { inTeam: false };

    } catch (error) {
      console.error('Erro ao verificar time do jogador:', error);
      return { inTeam: false };
    }
  }

  // Buscar todos os times de um jogador (por jogo)
  static async getPlayerTeams(playerId: string) {
    try {
      const teamMemberships = await prisma.teamMember.findMany({
        where: { playerId },
        include: {
          team: {
            include: {
              captain: {
                select: { id: true, name: true, email: true }
              },
              members: {
                include: {
                  player: {
                    select: { id: true, name: true, email: true }
                  }
                }
              }
            }
          }
        }
      });

      return teamMemberships.map(membership => ({
        id: membership.team.id,
        name: membership.team.name,
        game: membership.team.game,
        logo: membership.team.logo,
        captainId: membership.team.captainId,
        captain: membership.team.captain,
        members: membership.team.members.map(m => m.player),
        joinedAt: membership.joinedAt,
        createdAt: membership.team.createdAt
      }));

    } catch (error) {
      console.error('Erro ao buscar times do jogador:', error);
      return [];
    }
  }

  // Criar novo time
  static async createTeam(captainId: string, name: string, game: string, logo?: string) {
    try {
      // Verificar se o capitão já está em um time para este jogo
      const existingTeam = await this.isPlayerInTeamForGame(captainId, game);
      if (existingTeam.inTeam) {
        throw new Error(`Você já está no time "${existingTeam.teamName}" para ${game}`);
      }

      // Criar o time
      const team = await prisma.team.create({
        data: {
          name,
          game,
          logo,
          captainId,
          members: {
            create: {
              playerId: captainId
            }
          }
        },
        include: {
          captain: {
            select: { id: true, name: true, email: true }
          },
          members: {
            include: {
              player: {
                select: { id: true, name: true, email: true }
              }
            }
          }
        }
      });

      return {
        success: true,
        team: {
          id: team.id,
          name: team.name,
          game: team.game,
          logo: team.logo,
          captainId: team.captainId,
          captain: team.captain,
          members: team.members.map(m => m.player),
          createdAt: team.createdAt
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro ao criar time'
      };
    }
  }

  // Adicionar jogador ao time
  static async addPlayerToTeam(teamId: string, playerId: string) {
    try {
      // Buscar informações do time
      const team = await prisma.team.findUnique({
        where: { id: teamId },
        select: { game: true, name: true }
      });

      if (!team) {
        throw new Error('Time não encontrado');
      }

      // Verificar se o jogador já está em um time para este jogo
      const existingTeam = await this.isPlayerInTeamForGame(playerId, team.game);
      if (existingTeam.inTeam) {
        throw new Error(`Jogador já está no time "${existingTeam.teamName}" para ${team.game}`);
      }

      // Adicionar ao time
      await prisma.teamMember.create({
        data: {
          teamId,
          playerId
        }
      });

      return {
        success: true,
        message: `Jogador adicionado ao time ${team.name} com sucesso`
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro ao adicionar jogador ao time'
      };
    }
  }

  // Remover jogador do time
  static async removePlayerFromTeam(teamId: string, playerId: string) {
    try {
      const team = await prisma.team.findUnique({
        where: { id: teamId },
        select: { captainId: true, name: true }
      });

      if (!team) {
        throw new Error('Time não encontrado');
      }

      // Não permitir remover o capitão
      if (team.captainId === playerId) {
        throw new Error('O capitão não pode ser removido do time');
      }

      // Remover do time
      await prisma.teamMember.delete({
        where: {
          teamId_playerId: {
            teamId,
            playerId
          }
        }
      });

      return {
        success: true,
        message: `Jogador removido do time ${team.name} com sucesso`
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro ao remover jogador do time'
      };
    }
  }

  // Buscar times por jogo
  static async getTeamsByGame(game: string) {
    try {
      const teams = await prisma.team.findMany({
        where: { game },
        include: {
          captain: {
            select: { id: true, name: true, email: true }
          },
          members: {
            include: {
              player: {
                select: { id: true, name: true, email: true }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      return teams.map(team => ({
        id: team.id,
        name: team.name,
        game: team.game,
        logo: team.logo,
        captainId: team.captainId,
        captain: team.captain,
        members: team.members.map(m => m.player),
        memberCount: team.members.length,
        createdAt: team.createdAt
      }));

    } catch (error) {
      console.error('Erro ao buscar times por jogo:', error);
      return [];
    }
  }
}
