{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./server", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["./server/*"]}}, "include": ["server/**/*"], "exclude": ["node_modules", "dist", "src"], "ts-node": {"esm": false, "experimentalSpecifierResolution": "node"}}