import React, { useState } from 'react';
import { 
  Trophy, 
  Calendar, 
  Users, 
  DollarSign, 
  FileText, 
  Settings,
  Plus,
  Minus,
  Save
} from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { useAuth } from '../../contexts/AuthContext';

export function CreateTournament() {
  const { createTournament } = useApp();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  
  const [formData, setFormData] = useState({
    name: '',
    game: '',
    description: '',
    format: 'single-elimination' as 'single-elimination' | 'double-elimination' | 'groups' | 'swiss',
    maxParticipants: 16,
    registrationFee: 0,
    prizePool: 0,
    prizeDistribution: [
      { place: 1, percentage: 50 },
      { place: 2, percentage: 30 },
      { place: 3, percentage: 20 }
    ],
    rules: [''],
    platforms: [''],
    startDate: '',
    endDate: '',
    registrationDeadline: ''
  });

  const popularGames = [
    'Counter-Strike 2',
    'Valorant',
    'League of Legends',
    'Dota 2',
    'Rocket League',
    'Fortnite',
    'Apex Legends',
    'Overwatch 2'
  ];

  const formats = [
    { value: 'single-elimination', label: 'Mata-mata Simples', description: 'Eliminação direta' },
    { value: 'double-elimination', label: 'Mata-mata Dupla', description: 'Segunda chance para derrotados' },
    { value: 'groups', label: 'Fase de Grupos', description: 'Grupos seguidos de playoffs' },
    { value: 'swiss', label: 'Sistema Suíço', description: 'Emparelhamento baseado em pontos' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setLoading(true);
    try {
      await createTournament({
        ...formData,
        organizerId: user.id,
        rules: formData.rules.filter(rule => rule.trim() !== ''),
        platforms: formData.platforms.filter(platform => platform.trim() !== '')
      });
      
      // Reset form
      setFormData({
        name: '',
        game: '',
        description: '',
        format: 'single-elimination',
        maxParticipants: 16,
        registrationFee: 0,
        prizePool: 0,
        prizeDistribution: [
          { place: 1, percentage: 50 },
          { place: 2, percentage: 30 },
          { place: 3, percentage: 20 }
        ],
        rules: [''],
        platforms: [''],
        startDate: '',
        endDate: '',
        registrationDeadline: ''
      });
      setStep(1);
      
      alert('Torneio criado com sucesso!');
    } catch (error) {
      alert('Erro ao criar torneio');
    } finally {
      setLoading(false);
    }
  };

  const addRule = () => {
    setFormData({ ...formData, rules: [...formData.rules, ''] });
  };

  const removeRule = (index: number) => {
    const newRules = formData.rules.filter((_, i) => i !== index);
    setFormData({ ...formData, rules: newRules });
  };

  const updateRule = (index: number, value: string) => {
    const newRules = [...formData.rules];
    newRules[index] = value;
    setFormData({ ...formData, rules: newRules });
  };

  const addPlatform = () => {
    setFormData({ ...formData, platforms: [...formData.platforms, ''] });
  };

  const removePlatform = (index: number) => {
    const newPlatforms = formData.platforms.filter((_, i) => i !== index);
    setFormData({ ...formData, platforms: newPlatforms });
  };

  const updatePlatform = (index: number, value: string) => {
    const newPlatforms = [...formData.platforms];
    newPlatforms[index] = value;
    setFormData({ ...formData, platforms: newPlatforms });
  };

  const nextStep = () => setStep(step + 1);
  const prevStep = () => setStep(step - 1);

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="flex justify-center mb-4">
          <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center">
            <Trophy className="w-8 h-8 text-white" />
          </div>
        </div>
        <h1 className="text-3xl font-bold text-white mb-2">Criar Novo Torneio</h1>
        <p className="text-gray-400">Configure seu campeonato e comece a atrair participantes</p>
      </div>

      {/* Progress Steps */}
      <div className="flex items-center justify-center space-x-4">
        {[1, 2, 3, 4].map((stepNumber) => (
          <div key={stepNumber} className="flex items-center">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center font-medium ${
              step >= stepNumber 
                ? 'bg-purple-600 text-white' 
                : 'bg-gray-700 text-gray-400'
            }`}>
              {stepNumber}
            </div>
            {stepNumber < 4 && (
              <div className={`w-16 h-1 ${
                step > stepNumber ? 'bg-purple-600' : 'bg-gray-700'
              }`} />
            )}
          </div>
        ))}
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Step 1: Basic Information */}
        {step === 1 && (
          <div className="bg-gray-800 rounded-xl p-8 border border-gray-700">
            <div className="flex items-center space-x-3 mb-6">
              <FileText className="w-6 h-6 text-purple-400" />
              <h2 className="text-xl font-semibold text-white">Informações Básicas</h2>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="lg:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">Nome do Torneio</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="Ex: Liga CS2 Brasil 2024"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Jogo</label>
                <div className="space-y-2">
                  <select
                    value={formData.game}
                    onChange={(e) => setFormData({ ...formData, game: e.target.value })}
                    className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    required
                  >
                    <option value="">Selecione um jogo</option>
                    {popularGames.map(game => (
                      <option key={game} value={game}>{game}</option>
                    ))}
                    <option value="custom">Outro jogo</option>
                  </select>
                  {formData.game === 'custom' && (
                    <input
                      type="text"
                      placeholder="Digite o nome do jogo"
                      onChange={(e) => setFormData({ ...formData, game: e.target.value })}
                      className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Formato</label>
                <select
                  value={formData.format}
                  onChange={(e) => setFormData({ ...formData, format: e.target.value as any })}
                  className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  required
                >
                  {formats.map(format => (
                    <option key={format.value} value={format.value}>
                      {format.label} - {format.description}
                    </option>
                  ))}
                </select>
              </div>

              <div className="lg:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">Descrição</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={4}
                  className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                  placeholder="Descreva seu torneio, regras principais e informações importantes..."
                  required
                />
              </div>
            </div>
          </div>
        )}

        {/* Step 2: Participants & Pricing */}
        {step === 2 && (
          <div className="bg-gray-800 rounded-xl p-8 border border-gray-700">
            <div className="flex items-center space-x-3 mb-6">
              <Users className="w-6 h-6 text-purple-400" />
              <h2 className="text-xl font-semibold text-white">Participantes e Financeiro</h2>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Número Máximo de Participantes</label>
                <select
                  value={formData.maxParticipants}
                  onChange={(e) => setFormData({ ...formData, maxParticipants: parseInt(e.target.value) })}
                  className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  required
                >
                  <option value={8}>8 participantes</option>
                  <option value={16}>16 participantes</option>
                  <option value={32}>32 participantes</option>
                  <option value={64}>64 participantes</option>
                  <option value={128}>128 participantes</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Taxa de Inscrição (R$)</label>
                <input
                  type="number"
                  value={formData.registrationFee}
                  onChange={(e) => setFormData({ ...formData, registrationFee: parseFloat(e.target.value) || 0 })}
                  className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Premiação Total (R$)</label>
                <input
                  type="number"
                  value={formData.prizePool}
                  onChange={(e) => setFormData({ ...formData, prizePool: parseFloat(e.target.value) || 0 })}
                  className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Distribuição de Prêmios</label>
                <div className="space-y-2">
                  {formData.prizeDistribution.map((prize, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <span className="text-gray-400 text-sm w-12">{prize.place}º:</span>
                      <input
                        type="number"
                        value={prize.percentage}
                        onChange={(e) => {
                          const newDistribution = [...formData.prizeDistribution];
                          newDistribution[index].percentage = parseInt(e.target.value) || 0;
                          setFormData({ ...formData, prizeDistribution: newDistribution });
                        }}
                        className="flex-1 px-3 py-2 bg-gray-900 border border-gray-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        min="0"
                        max="100"
                      />
                      <span className="text-gray-400 text-sm">%</span>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  Total: {formData.prizeDistribution.reduce((sum, p) => sum + p.percentage, 0)}%
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Rules & Platforms */}
        {step === 3 && (
          <div className="bg-gray-800 rounded-xl p-8 border border-gray-700">
            <div className="flex items-center space-x-3 mb-6">
              <Settings className="w-6 h-6 text-purple-400" />
              <h2 className="text-xl font-semibold text-white">Regras e Plataformas</h2>
            </div>

            <div className="space-y-6">
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-300">Regras do Torneio</label>
                  <button
                    type="button"
                    onClick={addRule}
                    className="flex items-center space-x-1 text-purple-400 hover:text-purple-300 text-sm transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Adicionar regra</span>
                  </button>
                </div>
                <div className="space-y-3">
                  {formData.rules.map((rule, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <input
                        type="text"
                        value={rule}
                        onChange={(e) => updateRule(index, e.target.value)}
                        className="flex-1 px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        placeholder={`Regra ${index + 1}`}
                      />
                      {formData.rules.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeRule(index)}
                          className="p-2 text-red-400 hover:text-red-300 transition-colors"
                        >
                          <Minus className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-300">Plataformas de Jogo</label>
                  <button
                    type="button"
                    onClick={addPlatform}
                    className="flex items-center space-x-1 text-purple-400 hover:text-purple-300 text-sm transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Adicionar plataforma</span>
                  </button>
                </div>
                <div className="space-y-3">
                  {formData.platforms.map((platform, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <input
                        type="text"
                        value={platform}
                        onChange={(e) => updatePlatform(index, e.target.value)}
                        className="flex-1 px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        placeholder="Ex: Steam, Epic Games, Battle.net"
                      />
                      {formData.platforms.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removePlatform(index)}
                          className="p-2 text-red-400 hover:text-red-300 transition-colors"
                        >
                          <Minus className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Step 4: Schedule */}
        {step === 4 && (
          <div className="bg-gray-800 rounded-xl p-8 border border-gray-700">
            <div className="flex items-center space-x-3 mb-6">
              <Calendar className="w-6 h-6 text-purple-400" />
              <h2 className="text-xl font-semibold text-white">Cronograma</h2>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Prazo de Inscrição</label>
                <input
                  type="datetime-local"
                  value={formData.registrationDeadline}
                  onChange={(e) => setFormData({ ...formData, registrationDeadline: e.target.value })}
                  className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Data de Início</label>
                <input
                  type="datetime-local"
                  value={formData.startDate}
                  onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                  className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Data de Término</label>
                <input
                  type="datetime-local"
                  value={formData.endDate}
                  onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                  className="w-full px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  required
                />
              </div>
            </div>

            {/* Summary */}
            <div className="mt-8 p-6 bg-gray-900 rounded-xl border border-gray-600">
              <h3 className="text-lg font-medium text-white mb-4">Resumo do Torneio</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Nome:</span>
                  <span className="text-white ml-2">{formData.name || 'Não definido'}</span>
                </div>
                <div>
                  <span className="text-gray-400">Jogo:</span>
                  <span className="text-white ml-2">{formData.game || 'Não definido'}</span>
                </div>
                <div>
                  <span className="text-gray-400">Participantes:</span>
                  <span className="text-white ml-2">{formData.maxParticipants}</span>
                </div>
                <div>
                  <span className="text-gray-400">Premiação:</span>
                  <span className="text-white ml-2">R$ {formData.prizePool.toLocaleString('pt-BR')}</span>
                </div>
                <div>
                  <span className="text-gray-400">Taxa:</span>
                  <span className="text-white ml-2">R$ {formData.registrationFee.toLocaleString('pt-BR')}</span>
                </div>
                <div>
                  <span className="text-gray-400">Formato:</span>
                  <span className="text-white ml-2">{formats.find(f => f.value === formData.format)?.label}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex items-center justify-between">
          <div>
            {step > 1 && (
              <button
                type="button"
                onClick={prevStep}
                className="px-6 py-3 bg-gray-700 text-white rounded-xl hover:bg-gray-600 transition-colors duration-200"
              >
                Voltar
              </button>
            )}
          </div>
          
          <div>
            {step < 4 ? (
              <button
                type="button"
                onClick={nextStep}
                className="px-6 py-3 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-colors duration-200"
              >
                Próximo
              </button>
            ) : (
              <button
                type="submit"
                disabled={loading}
                className="flex items-center space-x-2 px-8 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                <Save className="w-5 h-5" />
                <span>{loading ? 'Criando...' : 'Criar Torneio'}</span>
              </button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
}