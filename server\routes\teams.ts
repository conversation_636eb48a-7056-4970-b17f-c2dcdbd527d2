import express from 'express';
import { TeamService } from '../services/teamService';
import { TeamInviteService } from '../services/teamInviteService';
import { authenticateToken, requireRole, AuthRequest } from '../middleware/auth';

const router = express.Router();

// GET /api/teams - Listar times por jogo
router.get('/', async (req, res) => {
  try {
    const { game } = req.query;

    if (!game || typeof game !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Parâmetro "game" é obrigatório'
      });
    }

    const teams = await TeamService.getTeamsByGame(game);

    res.json({
      success: true,
      teams
    });

  } catch (error) {
    console.error('Erro ao buscar times:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar times'
    });
  }
});

// POST /api/teams - Criar novo time
router.post('/', authenticateToken, requireRole(['PLAYER']), async (req: AuthRequest, res) => {
  try {
    const { name, game, logo } = req.body;
    const captainId = req.user!.id;

    if (!name || !game) {
      return res.status(400).json({
        success: false,
        error: 'Nome e jogo são obrigatórios'
      });
    }

    const result = await TeamService.createTeam(captainId, name, game, logo);

    if (result.success) {
      res.status(201).json(result);
    } else {
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('Erro ao criar time:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao criar time'
    });
  }
});

// GET /api/teams/my - Times do jogador logado
router.get('/my', authenticateToken, requireRole(['PLAYER']), async (req: AuthRequest, res) => {
  try {
    const playerId = req.user!.id;
    const teams = await TeamService.getPlayerTeams(playerId);

    res.json({
      success: true,
      teams
    });

  } catch (error) {
    console.error('Erro ao buscar times do jogador:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar times'
    });
  }
});

// POST /api/teams/:teamId/members - Adicionar jogador ao time
router.post('/:teamId/members', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { teamId } = req.params;
    const { playerId } = req.body;

    if (!playerId) {
      return res.status(400).json({
        success: false,
        error: 'ID do jogador é obrigatório'
      });
    }

    const result = await TeamService.addPlayerToTeam(teamId, playerId);

    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('Erro ao adicionar jogador ao time:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao adicionar jogador ao time'
    });
  }
});

// DELETE /api/teams/:teamId/members/:playerId - Remover jogador do time
router.delete('/:teamId/members/:playerId', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { teamId, playerId } = req.params;

    const result = await TeamService.removePlayerFromTeam(teamId, playerId);

    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('Erro ao remover jogador do time:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao remover jogador do time'
    });
  }
});

// GET /api/teams/check/:game - Verificar se jogador está em time para um jogo
router.get('/check/:game', authenticateToken, requireRole(['PLAYER']), async (req: AuthRequest, res) => {
  try {
    const { game } = req.params;
    const playerId = req.user!.id;

    const result = await TeamService.isPlayerInTeamForGame(playerId, game);

    res.json({
      success: true,
      ...result
    });

  } catch (error) {
    console.error('Erro ao verificar time do jogador:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao verificar time'
    });
  }
});

// ================================
// ROTAS DE CONVITES
// ================================

// POST /api/teams/:teamId/invite - Enviar convite para jogador
router.post('/:teamId/invite', authenticateToken, requireRole(['PLAYER']), async (req: AuthRequest, res) => {
  try {
    const { teamId } = req.params;
    const { playerId, message } = req.body;
    const invitedBy = req.user!.id;

    if (!playerId) {
      return res.status(400).json({
        success: false,
        error: 'ID do jogador é obrigatório'
      });
    }

    const result = await TeamInviteService.sendInvite(teamId, playerId, invitedBy, message);

    if (result.success) {
      res.status(201).json(result);
    } else {
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('Erro ao enviar convite:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao enviar convite'
    });
  }
});

// GET /api/teams/invites/received - Convites recebidos pelo jogador
router.get('/invites/received', authenticateToken, requireRole(['PLAYER']), async (req: AuthRequest, res) => {
  try {
    const playerId = req.user!.id;
    const invites = await TeamInviteService.getReceivedInvites(playerId);

    res.json({
      success: true,
      invites
    });

  } catch (error) {
    console.error('Erro ao buscar convites recebidos:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar convites'
    });
  }
});

// GET /api/teams/:teamId/invites - Convites enviados pelo time
router.get('/:teamId/invites', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { teamId } = req.params;
    const invites = await TeamInviteService.getSentInvites(teamId);

    res.json({
      success: true,
      invites
    });

  } catch (error) {
    console.error('Erro ao buscar convites enviados:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar convites'
    });
  }
});

// POST /api/teams/invites/:inviteId/respond - Responder a convite
router.post('/invites/:inviteId/respond', authenticateToken, requireRole(['PLAYER']), async (req: AuthRequest, res) => {
  try {
    const { inviteId } = req.params;
    const { response } = req.body;
    const playerId = req.user!.id;

    if (!response || !['ACCEPTED', 'DECLINED'].includes(response)) {
      return res.status(400).json({
        success: false,
        error: 'Resposta deve ser ACCEPTED ou DECLINED'
      });
    }

    const result = await TeamInviteService.respondToInvite(inviteId, playerId, response);

    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('Erro ao responder convite:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao responder convite'
    });
  }
});

// DELETE /api/teams/invites/:inviteId - Cancelar convite
router.delete('/invites/:inviteId', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { inviteId } = req.params;
    const userId = req.user!.id;

    const result = await TeamInviteService.cancelInvite(inviteId, userId);

    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('Erro ao cancelar convite:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao cancelar convite'
    });
  }
});

export { router as teamRoutes };
