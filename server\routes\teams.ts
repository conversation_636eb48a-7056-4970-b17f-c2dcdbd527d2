import express from 'express';
import { TeamService } from '../services/teamService';
import { authenticateToken, requireRole, AuthRequest } from '../middleware/auth';

const router = express.Router();

// GET /api/teams - Listar times por jogo
router.get('/', async (req, res) => {
  try {
    const { game } = req.query;

    if (!game || typeof game !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Parâmetro "game" é obrigatório'
      });
    }

    const teams = await TeamService.getTeamsByGame(game);

    res.json({
      success: true,
      teams
    });

  } catch (error) {
    console.error('Erro ao buscar times:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar times'
    });
  }
});

// POST /api/teams - Criar novo time
router.post('/', authenticateToken, requireRole(['PLAYER']), async (req: AuthRequest, res) => {
  try {
    const { name, game, logo } = req.body;
    const captainId = req.user!.id;

    if (!name || !game) {
      return res.status(400).json({
        success: false,
        error: 'Nome e jogo são obrigatórios'
      });
    }

    const result = await TeamService.createTeam(captainId, name, game, logo);

    if (result.success) {
      res.status(201).json(result);
    } else {
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('Erro ao criar time:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao criar time'
    });
  }
});

// GET /api/teams/my - Times do jogador logado
router.get('/my', authenticateToken, requireRole(['PLAYER']), async (req: AuthRequest, res) => {
  try {
    const playerId = req.user!.id;
    const teams = await TeamService.getPlayerTeams(playerId);

    res.json({
      success: true,
      teams
    });

  } catch (error) {
    console.error('Erro ao buscar times do jogador:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar times'
    });
  }
});

// POST /api/teams/:teamId/members - Adicionar jogador ao time
router.post('/:teamId/members', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { teamId } = req.params;
    const { playerId } = req.body;

    if (!playerId) {
      return res.status(400).json({
        success: false,
        error: 'ID do jogador é obrigatório'
      });
    }

    const result = await TeamService.addPlayerToTeam(teamId, playerId);

    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('Erro ao adicionar jogador ao time:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao adicionar jogador ao time'
    });
  }
});

// DELETE /api/teams/:teamId/members/:playerId - Remover jogador do time
router.delete('/:teamId/members/:playerId', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { teamId, playerId } = req.params;

    const result = await TeamService.removePlayerFromTeam(teamId, playerId);

    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }

  } catch (error) {
    console.error('Erro ao remover jogador do time:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao remover jogador do time'
    });
  }
});

// GET /api/teams/check/:game - Verificar se jogador está em time para um jogo
router.get('/check/:game', authenticateToken, requireRole(['PLAYER']), async (req: AuthRequest, res) => {
  try {
    const { game } = req.params;
    const playerId = req.user!.id;

    const result = await TeamService.isPlayerInTeamForGame(playerId, game);

    res.json({
      success: true,
      ...result
    });

  } catch (error) {
    console.error('Erro ao verificar time do jogador:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao verificar time'
    });
  }
});

export { router as teamRoutes };
