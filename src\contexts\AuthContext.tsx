import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, Player } from '../types';

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string, type: 'organizer' | 'player') => Promise<void>;
  register: (email: string, password: string, name: string, type: 'organizer' | 'player') => Promise<void>;
  logout: () => void;
  updateProfile: (updates: Partial<User>) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock data for demo
const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: '<PERSON>',
    type: 'organizer',
    createdAt: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: '<PERSON>',
    type: 'player',
    createdAt: '2024-01-01T00:00:00Z'
  } as Player
];

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
  }, []);

  const login = async (email: string, password: string, type: 'organizer' | 'player') => {
    // Mock login - in real app, this would be an API call
    const foundUser = mockUsers.find(u => u.email === email && u.type === type);
    if (foundUser) {
      setUser(foundUser);
      localStorage.setItem('currentUser', JSON.stringify(foundUser));
    } else {
      throw new Error('Credenciais inválidas');
    }
  };

  const register = async (email: string, password: string, name: string, type: 'organizer' | 'player') => {
    // Mock registration
    const newUser: User = {
      id: Date.now().toString(),
      email,
      name,
      type,
      createdAt: new Date().toISOString()
    };
    
    mockUsers.push(newUser);
    setUser(newUser);
    localStorage.setItem('currentUser', JSON.stringify(newUser));
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('currentUser');
  };

  const updateProfile = (updates: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...updates };
      setUser(updatedUser);
      localStorage.setItem('currentUser', JSON.stringify(updatedUser));
    }
  };

  return (
    <AuthContext.Provider value={{ user, login, register, logout, updateProfile }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}