import React, { createContext, useContext, useState, useEffect } from 'react';
import { User } from '../types';
import { apiService } from '../services/apiService';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string, type: 'organizer' | 'player') => Promise<void>;
  register: (email: string, password: string, name: string, type: 'organizer' | 'player') => Promise<void>;
  logout: () => void;
  updateProfile: (updates: Partial<User>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Verificar se há token salvo
    const token = localStorage.getItem('authToken');
    if (token) {
      apiService.getMe().then(response => {
        if (response.success && response.user) {
          setUser(response.user);
        } else {
          localStorage.removeItem('authToken');
        }
        setLoading(false);
      }).catch(() => {
        localStorage.removeItem('authToken');
        setLoading(false);
      });
    } else {
      setLoading(false);
    }
  }, []);

  const login = async (email: string, password: string, type: 'organizer' | 'player') => {
    const response = await apiService.login(email, password, type);

    if (response.success && response.user && response.token) {
      setUser(response.user);
      localStorage.setItem('authToken', response.token);
    } else {
      throw new Error(response.error || 'Erro no login');
    }
  };

  const register = async (email: string, password: string, name: string, type: 'organizer' | 'player') => {
    const response = await apiService.register(email, password, name, type);

    if (response.success && response.user && response.token) {
      setUser(response.user);
      localStorage.setItem('authToken', response.token);
    } else {
      throw new Error(response.error || 'Erro no registro');
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('authToken');
  };

  const updateProfile = async (updates: Partial<User>) => {
    if (user) {
      const response = await apiService.updateUserProfile(updates);
      if (response.success && response.user) {
        setUser(response.user);
      } else {
        throw new Error(response.error || 'Erro ao atualizar perfil');
      }
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, login, register, logout, updateProfile }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}