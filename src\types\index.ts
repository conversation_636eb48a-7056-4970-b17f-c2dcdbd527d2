export interface User {
  id: string;
  email: string;
  name: string;
  type: 'organizer' | 'player';
  avatar?: string;
  createdAt: string;
}

export interface Player extends User {
  type: 'player';
  age?: number;
  peripherals?: string;
  preferredGames: string[];
  rank?: string;
  socialLinks?: {
    twitch?: string;
    youtube?: string;
    discord?: string;
  };
  teamId?: string;
  achievements: Achievement[];
}

export interface Team {
  id: string;
  name: string;
  logo?: string;
  members: string[]; // player IDs
  captainId: string;
  achievements: Achievement[];
  createdAt: string;
}

export interface Tournament {
  id: string;
  name: string;
  game: string;
  organizerId: string;
  description: string;
  format: 'single-elimination' | 'double-elimination' | 'groups' | 'swiss';
  maxParticipants: number;
  registrationFee: number;
  prizePool: number;
  prizeDistribution: { place: number; percentage: number }[];
  rules: string[];
  platforms: string[];
  startDate: string;
  endDate: string;
  registrationDeadline: string;
  status: 'upcoming' | 'registration' | 'ongoing' | 'completed' | 'cancelled';
  participants: string[]; // team/player IDs
  bracket?: BracketMatch[];
  createdAt: string;
}

export interface BracketMatch {
  id: string;
  round: number;
  position: number;
  participant1?: string;
  participant2?: string;
  winner?: string;
  score?: { p1: number; p2: number };
  status: 'pending' | 'ongoing' | 'completed';
  scheduledAt?: string;
}

export interface Achievement {
  tournamentId: string;
  tournamentName: string;
  place: number;
  date: string;
  prize?: number;
}

export interface Notification {
  id: string;
  userId: string;
  type: 'tournament' | 'match' | 'system';
  title: string;
  message: string;
  read: boolean;
  createdAt: string;
}