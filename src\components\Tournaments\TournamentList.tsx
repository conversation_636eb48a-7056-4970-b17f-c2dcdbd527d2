import React, { useState } from 'react';
import { 
  Trophy, 
  Users, 
  Calendar, 
  DollarSign, 
  Filter, 
  Search,
  MapPin,
  Clock,
  Star
} from 'lucide-react';
import { useApp } from '../../contexts/AppContext';
import { useAuth } from '../../contexts/AuthContext';
import { Tournament } from '../../types';
import { TournamentRegistration } from './TournamentRegistration';

export function TournamentList() {
  const { tournaments, registerForTournament, refreshTournaments, loading } = useApp();
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGame, setSelectedGame] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedTournament, setSelectedTournament] = useState<Tournament | null>(null);
  const [showRegistration, setShowRegistration] = useState(false);
  const [sortBy, setSortBy] = useState('startDate');

  const games = [...new Set(tournaments.map(t => t.game))];
  const statuses = ['registration', 'upcoming', 'ongoing', 'completed'];

  const filteredTournaments = tournaments
    .filter(tournament => {
      const matchesSearch = tournament.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           tournament.game.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesGame = selectedGame === 'all' || tournament.game === selectedGame;
      const matchesStatus = selectedStatus === 'all' || tournament.status === selectedStatus;
      
      return matchesSearch && matchesGame && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'prizePool':
          return b.prizePool - a.prizePool;
        case 'participants':
          return b.participants.length - a.participants.length;
        case 'startDate':
        default:
          return new Date(a.startDate).getTime() - new Date(b.startDate).getTime();
      }
    });



  const handleRegisterClick = (tournament: Tournament) => {
    setSelectedTournament(tournament);
    setShowRegistration(true);
  };

  const handleRegistrationComplete = () => {
    setShowRegistration(false);
    setSelectedTournament(null);
    refreshTournaments();
  };

  const handleRegistrationCancel = () => {
    setShowRegistration(false);
    setSelectedTournament(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'registration':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'upcoming':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'ongoing':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'completed':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'registration':
        return 'Inscrições Abertas';
      case 'upcoming':
        return 'Em Breve';
      case 'ongoing':
        return 'Em Andamento';
      case 'completed':
        return 'Finalizado';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-16">
          <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">Carregando torneios...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white">Campeonatos</h1>
          <p className="text-gray-400 mt-1">Encontre e participe dos melhores torneios de e-sports</p>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-400">{filteredTournaments.length} torneios encontrados</span>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar por nome ou jogo..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Game Filter */}
          <select
            value={selectedGame}
            onChange={(e) => setSelectedGame(e.target.value)}
            className="px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="">Todos os jogos</option>
            {games.map(game => (
              <option key={game} value={game}>{game}</option>
            ))}
          </select>

          {/* Status Filter */}
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="">Todos os status</option>
            {statuses.map(status => (
              <option key={status} value={status}>{getStatusText(status)}</option>
            ))}
          </select>

          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-4 py-3 bg-gray-900 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="startDate">Data de início</option>
            <option value="prizePool">Premiação</option>
            <option value="participants">Participantes</option>
          </select>
        </div>
      </div>

      {/* Tournament Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredTournaments.map((tournament) => (
          <TournamentCard
            key={tournament.id}
            tournament={tournament}
            onRegister={() => handleRegisterClick(tournament)}
            isRegistered={tournament.participants.includes(user?.id || '')}
            canRegister={user?.type === 'player' && tournament.status === 'registration'}
          />
        ))}
      </div>

      {filteredTournaments.length === 0 && (
        <div className="text-center py-16">
          <Trophy className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-400 mb-2">Nenhum torneio encontrado</h3>
          <p className="text-gray-500">Tente ajustar os filtros ou buscar por outros termos</p>
        </div>
      )}

      {/* Modal de Inscrição */}
      {showRegistration && selectedTournament && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <TournamentRegistration
              tournament={selectedTournament}
              onRegistrationComplete={handleRegistrationComplete}
              onCancel={handleRegistrationCancel}
            />
          </div>
        </div>
      )}
    </div>
  );
}

interface TournamentCardProps {
  tournament: Tournament;
  onRegister: () => void;
  isRegistered: boolean;
  canRegister: boolean;
}

function TournamentCard({ tournament, onRegister, isRegistered, canRegister }: TournamentCardProps) {
  const spotsLeft = tournament.maxParticipants - tournament.participants.length;
  const isFull = spotsLeft <= 0;

  return (
    <div className="bg-gray-800 rounded-xl border border-gray-700 hover:border-gray-600 transition-all duration-200 overflow-hidden group">
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
              <Trophy className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-white text-lg group-hover:text-purple-400 transition-colors">
                {tournament.name}
              </h3>
              <p className="text-gray-400 text-sm">{tournament.game}</p>
            </div>
          </div>
          <div className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(tournament.status)}`}>
            {getStatusText(tournament.status)}
          </div>
        </div>

        <p className="text-gray-300 text-sm line-clamp-2 mb-4">{tournament.description}</p>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="flex items-center space-x-2">
            <DollarSign className="w-4 h-4 text-green-400" />
            <div>
              <p className="text-xs text-gray-400">Premiação</p>
              <p className="text-sm font-medium text-white">
                R$ {tournament.prizePool.toLocaleString('pt-BR')}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Users className="w-4 h-4 text-blue-400" />
            <div>
              <p className="text-xs text-gray-400">Participantes</p>
              <p className="text-sm font-medium text-white">
                {tournament.participants.length}/{tournament.maxParticipants}
              </p>
            </div>
          </div>
        </div>

        {/* Date and Entry Fee */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <Calendar className="w-4 h-4" />
            <span>{new Date(tournament.startDate).toLocaleDateString('pt-BR', {
              day: '2-digit',
              month: 'short',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</span>
          </div>
          {tournament.registrationFee > 0 && (
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <DollarSign className="w-4 h-4" />
              <span>Taxa: R$ {tournament.registrationFee.toLocaleString('pt-BR')}</span>
            </div>
          )}
        </div>

        {/* Action Button */}
        <div className="pt-4 border-t border-gray-700">
          {isRegistered ? (
            <div className="flex items-center justify-center space-x-2 py-3 bg-green-500/20 text-green-400 rounded-lg border border-green-500/30">
              <Star className="w-4 h-4" />
              <span className="font-medium">Inscrito</span>
            </div>
          ) : canRegister ? (
            <button
              onClick={onRegister}
              disabled={isFull}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
                isFull
                  ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
                  : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 hover:shadow-lg'
              }`}
            >
              {isFull ? 'Lotado' : 'Inscrever-se'}
            </button>
          ) : (
            <div className="text-center py-3 text-gray-400 text-sm">
              {tournament.status === 'completed' ? 'Finalizado' : 'Visualizar apenas'}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function getStatusColor(status: string) {
  switch (status) {
    case 'registration':
      return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
    case 'upcoming':
      return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
    case 'ongoing':
      return 'bg-green-500/20 text-green-400 border-green-500/30';
    case 'completed':
      return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    default:
      return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
  }
}

function getStatusText(status: string) {
  switch (status) {
    case 'registration':
      return 'Inscrições Abertas';
    case 'upcoming':
      return 'Em Breve';
    case 'ongoing':
      return 'Em Andamento';
    case 'completed':
      return 'Finalizado';
    default:
      return status;
  }
}