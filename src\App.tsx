import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { AppProvider } from './contexts/AppContext';
import { Header } from './components/Layout/Header';
import { LoginForm } from './components/Auth/LoginForm';
import { RegisterForm } from './components/Auth/RegisterForm';
import { OrganizerDashboard } from './components/Dashboard/OrganizerDashboard';
import { PlayerDashboard } from './components/Dashboard/PlayerDashboard';
import { TournamentList } from './components/Tournaments/TournamentList';
import { CreateTournament } from './components/Tournaments/CreateTournament';

function AppContent() {
  const { user, loading } = useAuth();
  const [currentView, setCurrentView] = useState('dashboard');
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white text-lg">Carregando...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center p-4">
        <div className="w-full max-w-lg">
          {authMode === 'login' ? (
            <LoginForm onSwitchToRegister={() => setAuthMode('register')} />
          ) : (
            <RegisterForm onSwitchToLogin={() => setAuthMode('login')} />
          )}
        </div>
      </div>
    );
  }

  const renderContent = () => {
    switch (currentView) {
      case 'dashboard':
        return user.type === 'organizer' ? (
          <OrganizerDashboard onViewChange={setCurrentView} />
        ) : (
          <PlayerDashboard onViewChange={setCurrentView} />
        );
      case 'tournaments':
        return <TournamentList />;
      case 'create-tournament':
        return user.type === 'organizer' ? <CreateTournament /> : <TournamentList />;
      case 'teams':
        return (
          <div className="text-center py-16">
            <h2 className="text-2xl font-bold text-white mb-4">Times</h2>
            <p className="text-gray-400">Funcionalidade em desenvolvimento</p>
          </div>
        );
      case 'profile':
        return (
          <div className="text-center py-16">
            <h2 className="text-2xl font-bold text-white mb-4">Perfil</h2>
            <p className="text-gray-400">Funcionalidade em desenvolvimento</p>
          </div>
        );
      default:
        return user.type === 'organizer' ? (
          <OrganizerDashboard onViewChange={setCurrentView} />
        ) : (
          <PlayerDashboard onViewChange={setCurrentView} />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <Header currentView={currentView} onViewChange={setCurrentView} />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderContent()}
      </main>
    </div>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppProvider>
        <AppContent />
      </AppProvider>
    </AuthProvider>
  );
}

export default App;