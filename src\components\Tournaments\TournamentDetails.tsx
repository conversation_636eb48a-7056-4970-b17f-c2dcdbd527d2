import React, { useState, useEffect } from 'react';
import { 
  ArrowLeft, Users, Trophy, Calendar, DollarSign, MapPin, 
  Clock, User, Building, Globe, Twitter, Instagram, 
  MessageCircle, Eye, Play, AlertCircle 
} from 'lucide-react';
import { apiService } from '../../services/apiService';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../contexts/ToastContext';

interface TournamentDetailsData {
  id: string;
  name: string;
  game: string;
  description: string;
  format: string;
  maxParticipants: number;
  registrationFee: number;
  prizePool: number;
  rules: string[];
  platforms: string[];
  startDate: string;
  endDate: string;
  registrationDeadline: string;
  status: string;
  organizer: {
    id: string;
    name: string;
    email: string;
    // Dados básicos que já vêm do torneio
  };
  registrations?: Array<{
    id: string;
    participantType: string;
    participant: any;
  }>;
  prizeDistributions?: Array<{
    place: number;
    percentage: number;
  }>;
  _count?: {
    registrations: number;
  };
}

interface TournamentDetailsProps {
  tournamentId: string;
  onBack: () => void;
}

export function TournamentDetails({ tournamentId, onBack }: TournamentDetailsProps) {
  const { user } = useAuth();
  const toast = useToast();
  const [tournament, setTournament] = useState<TournamentDetailsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'details' | 'organizer' | 'participants' | 'competition'>('details');

  useEffect(() => {
    loadTournamentDetails();
  }, [tournamentId]);

  const loadTournamentDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Por enquanto, vamos buscar da lista de torneios
      const response = await apiService.getTournaments();
      
      if (response.success) {
        const foundTournament = response.tournaments?.find((t: any) => t.id === tournamentId);
        if (foundTournament) {
          setTournament(foundTournament);
        } else {
          setError('Torneio não encontrado');
        }
      } else {
        setError(response.error || 'Erro ao carregar detalhes do torneio');
      }
    } catch (err) {
      setError('Erro ao carregar detalhes do torneio');
      console.error('Erro ao carregar detalhes:', err);
    } finally {
      setLoading(false);
    }
  };

  const canViewCompetition = () => {
    if (!tournament || !user) return false;

    // Organizador sempre pode ver
    if (user.id === tournament.organizer?.id) return true;

    // Participantes podem ver se o torneio começou ou está pronto
    const isParticipant = tournament.registrations?.some(reg =>
      reg.participant?.id === user.id
    ) || false;

    return isParticipant && ['READY_TO_START', 'ONGOING', 'COMPLETED'].includes(tournament.status);
  };

  const getStatusInfo = () => {
    if (!tournament) return { color: 'gray', text: 'Carregando...' };
    
    switch (tournament.status) {
      case 'REGISTRATION':
        return { color: 'blue', text: 'Inscrições Abertas' };
      case 'WAITING_FOR_PARTICIPANTS':
        return { color: 'yellow', text: 'Aguardando Participantes' };
      case 'READY_TO_START':
        return { color: 'green', text: 'Pronto para Começar' };
      case 'ONGOING':
        return { color: 'purple', text: 'Em Andamento' };
      case 'COMPLETED':
        return { color: 'gray', text: 'Finalizado' };
      default:
        return { color: 'gray', text: tournament.status };
    }
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-gray-400">Carregando detalhes do torneio...</p>
      </div>
    );
  }

  if (error || !tournament) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
        <p className="text-red-400 mb-4">{error || 'Torneio não encontrado'}</p>
        <button
          onClick={onBack}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Voltar
        </button>
      </div>
    );
  }

  const statusInfo = getStatusInfo();
  const isOrganizer = user && tournament.organizer && user.id === tournament.organizer.id;
  const isParticipant = user && tournament.registrations?.some(reg =>
    reg.participant?.id === user.id
  ) || false;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={onBack}
          className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>
        
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <h2 className="text-2xl font-bold text-white">{tournament.name}</h2>
            <span className={`px-3 py-1 rounded-full text-xs font-medium bg-${statusInfo.color}-500/20 text-${statusInfo.color}-400 border border-${statusInfo.color}-500/30`}>
              {statusInfo.text}
            </span>
          </div>
          <div className="flex items-center space-x-4 text-gray-400">
            <span>{tournament.game || 'Jogo não especificado'}</span>
            <span>{tournament._count?.registrations || 0}/{tournament.maxParticipants || 0} participantes</span>
            <span>{tournament.startDate ? new Date(tournament.startDate).toLocaleDateString('pt-BR') : 'Data não definida'}</span>
          </div>
        </div>

        {canViewCompetition() && (
          <button
            onClick={() => setActiveTab('competition')}
            className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200"
          >
            <Play className="w-4 h-4" />
            <span>Ver Competição</span>
          </button>
        )}
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-800 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('details')}
          className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'details'
              ? 'bg-purple-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          <Eye className="w-4 h-4" />
          <span>Detalhes</span>
        </button>

        <button
          onClick={() => setActiveTab('organizer')}
          className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'organizer'
              ? 'bg-purple-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          <User className="w-4 h-4" />
          <span>Organizador</span>
        </button>

        <button
          onClick={() => setActiveTab('participants')}
          className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'participants'
              ? 'bg-purple-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          <Users className="w-4 h-4" />
          <span>Participantes ({tournament._count?.registrations || 0})</span>
        </button>

        {canViewCompetition() && (
          <button
            onClick={() => setActiveTab('competition')}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'competition'
                ? 'bg-purple-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-700'
            }`}
          >
            <Trophy className="w-4 h-4" />
            <span>Competição</span>
          </button>
        )}
      </div>

      {/* Content */}
      <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        {activeTab === 'details' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Informações do Torneio</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm text-gray-400">Formato</label>
                    <p className="text-white font-medium">{tournament.format}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Participantes</label>
                    <p className="text-white font-medium">{tournament._count?.registrations || 0}/{tournament.maxParticipants || 0}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Taxa de Inscrição</label>
                    <p className="text-white font-medium">
                      {tournament.registrationFee > 0 ? `R$ ${tournament.registrationFee}` : 'Gratuito'}
                    </p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm text-gray-400">Premiação</label>
                    <p className="text-white font-medium">R$ {tournament.prizePool}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Data de Início</label>
                    <p className="text-white font-medium">{new Date(tournament.startDate).toLocaleDateString('pt-BR')}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-400">Prazo de Inscrição</label>
                    <p className="text-white font-medium">{new Date(tournament.registrationDeadline).toLocaleDateString('pt-BR')}</p>
                  </div>
                </div>
              </div>
            </div>

            {tournament.description && (
              <div>
                <h4 className="text-lg font-semibold text-white mb-3">Descrição</h4>
                <p className="text-gray-300">{tournament.description}</p>
              </div>
            )}

            {tournament.rules && tournament.rules.length > 0 && (
              <div>
                <h4 className="text-lg font-semibold text-white mb-3">Regras</h4>
                <ul className="space-y-2">
                  {tournament.rules.map((rule, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <span className="text-purple-500 mt-1">•</span>
                      <span className="text-gray-300">{rule}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {tournament.prizeDistributions && tournament.prizeDistributions.length > 0 && (
              <div>
                <h4 className="text-lg font-semibold text-white mb-3">Distribuição de Prêmios</h4>
                <div className="space-y-2">
                  {tournament.prizeDistributions.map((prize) => (
                    <div key={prize.place} className="flex justify-between items-center p-3 bg-gray-700 rounded-lg">
                      <span className="text-white font-medium">{prize.place}º Lugar</span>
                      <span className="text-green-400 font-medium">
                        {prize.percentage}% (R$ {(tournament.prizePool * prize.percentage / 100).toFixed(2)})
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'organizer' && (
          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
                <User className="w-8 h-8 text-white" />
              </div>

              <div className="flex-1">
                <h3 className="text-xl font-semibold text-white">{tournament.organizer?.name || 'Organizador'}</h3>
                <p className="text-gray-500 text-sm">{tournament.organizer?.email || ''}</p>
                <div className="mt-2">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-500/20 text-purple-400">
                    Organizador Verificado
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-gray-700 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-white mb-3">Sobre este Organizador</h4>
              <p className="text-gray-300">
                Organizador experiente na plataforma CampMaker. Responsável pela criação e gestão deste torneio.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-700 rounded-lg p-4">
                <h5 className="font-medium text-white mb-2">Contato</h5>
                <p className="text-gray-300 text-sm">{tournament.organizer?.email}</p>
              </div>

              <div className="bg-gray-700 rounded-lg p-4">
                <h5 className="font-medium text-white mb-2">Plataforma</h5>
                <p className="text-gray-300 text-sm">CampMaker Esports</p>
              </div>
            </div>

            <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
              <p className="text-blue-400 text-sm">
                💡 <strong>Dica:</strong> Entre em contato com o organizador através do email acima para dúvidas sobre o torneio.
              </p>
            </div>
          </div>
        )}

        {activeTab === 'participants' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white">Participantes Inscritos</h3>
              <span className="text-gray-400 text-sm">
                {tournament._count?.registrations || 0}/{tournament.maxParticipants || 0} vagas preenchidas
              </span>
            </div>

            {(tournament._count?.registrations || 0) < (tournament.maxParticipants || 0) && (
              <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                <p className="text-yellow-400 text-sm">
                  ⏳ Aguardando mais {(tournament.maxParticipants || 0) - (tournament._count?.registrations || 0)} participante{((tournament.maxParticipants || 0) - (tournament._count?.registrations || 0)) !== 1 ? 's' : ''} para iniciar a competição
                </p>
              </div>
            )}

            <div className="space-y-3">
              {(tournament.registrations || []).map((registration, index) => (
                <div
                  key={registration.id}
                  className="flex items-center justify-between p-4 bg-gray-700 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-medium text-sm">
                        {index + 1}
                      </span>
                    </div>
                    <div>
                      <p className="text-white font-medium">
                        {registration.participant.name || 'Participante'}
                      </p>
                      <p className="text-gray-400 text-sm">
                        {registration.participantType === 'TEAM' ? 'Time' : 'Individual'}
                      </p>
                    </div>
                  </div>
                  
                  {registration.participantType === 'TEAM' && (
                    <span className="text-purple-400 text-sm">
                      {registration.participant.members?.length || 0} membros
                    </span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'competition' && canViewCompetition() && (
          <div className="space-y-6">
            <div className="text-center py-12">
              <Trophy className="w-16 h-16 text-purple-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Área da Competição</h3>
              <p className="text-gray-400 mb-6">
                Aqui você verá os brackets, confrontos e resultados em tempo real
              </p>
              <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                <p className="text-blue-400 text-sm">
                  🚧 Sistema de brackets em desenvolvimento
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
