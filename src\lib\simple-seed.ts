import { prisma } from './prisma';
import bcrypt from 'bcryptjs';

async function main() {
  console.log('🌱 Iniciando seed simples...');

  try {
    // Criar organizador
    const organizer = await prisma.user.create({
      data: {
        id: 'org-1',
        email: '<EMAIL>',
        password: await bcrypt.hash('123456', 10),
        name: 'João Organizador',
        type: 'ORGANIZER'
      }
    });
    console.log('✅ Organizador criado:', organizer.name);

    // Criar jogador
    const player = await prisma.user.create({
      data: {
        id: 'player-1',
        email: '<EMAIL>',
        password: await bcrypt.hash('123456', 10),
        name: '<PERSON>',
        type: 'PLAYER'
      }
    });
    console.log('✅ Jogador criado:', player.name);

    // Criar torneio
    const tournament = await prisma.tournament.create({
      data: {
        id: 'tournament-1',
        name: 'Torneio Teste CS2',
        game: 'Counter-Strike 2',
        organizerId: organizer.id,
        description: 'Torneio de teste',
        format: 'SINGLE_ELIMINATION',
        maxParticipants: 16,
        registrationFee: 50,
        prizePool: 1000,
        rules: ['Sem trapaça', 'Respeito'],
        platforms: ['Steam'],
        startDate: new Date('2024-03-15'),
        endDate: new Date('2024-03-17'),
        registrationDeadline: new Date('2024-03-10'),
        status: 'REGISTRATION'
      }
    });
    console.log('✅ Torneio criado:', tournament.name);

    console.log('🎉 Seed concluído!');

  } catch (error) {
    console.error('❌ Erro no seed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
