import { prisma } from './prisma';
import bcrypt from 'bcryptjs';

async function main() {
  console.log('🌱 Iniciando seed simples...');

  try {
    // Limpar dados existentes primeiro
    await prisma.tournamentRegistration.deleteMany();
    await prisma.prizeDistribution.deleteMany();
    await prisma.tournament.deleteMany();
    await prisma.playerProfile.deleteMany();
    await prisma.user.deleteMany();

    // Criar organizador
    const organizer = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: await bcrypt.hash('123456', 10),
        name: '<PERSON>',
        type: 'ORGANIZER'
      }
    });
    console.log('✅ Organizador criado:', organizer.name);

    // Criar jogador
    const player = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: await bcrypt.hash('123456', 10),
        name: '<PERSON>',
        type: 'P<PERSON>Y<PERSON>',
        playerProfile: {
          create: {
            preferredGames: ['Counter-Strike 2', 'Valorant'],
            age: 22,
            rank: 'Global Elite'
          }
        }
      }
    });
    console.log('✅ Jogador criado:', player.name);

    // Criar torneio
    const tournament = await prisma.tournament.create({
      data: {
        name: 'Liga CS2 Brasil 2024',
        game: 'Counter-Strike 2',
        organizerId: organizer.id,
        description: 'Campeonato nacional de CS2 com as melhores equipes do país.',
        format: 'SINGLE_ELIMINATION',
        maxParticipants: 16,
        registrationFee: 50,
        prizePool: 5000,
        rules: ['Times de 5 jogadores', 'Modo Competitivo', 'Sem trapaça'],
        platforms: ['Steam'],
        startDate: new Date('2024-03-15T10:00:00Z'),
        endDate: new Date('2024-03-17T22:00:00Z'),
        registrationDeadline: new Date('2024-03-10T23:59:59Z'),
        status: 'REGISTRATION',
        prizeDistributions: {
          create: [
            { place: 1, percentage: 50 },
            { place: 2, percentage: 30 },
            { place: 3, percentage: 20 }
          ]
        }
      }
    });
    console.log('✅ Torneio criado:', tournament.name);

    // Registrar jogador no torneio (comentado por enquanto devido ao erro de FK)
    // await prisma.tournamentRegistration.create({
    //   data: {
    //     tournamentId: tournament.id,
    //     participantId: player.id,
    //     participantType: 'PLAYER'
    //   }
    // });
    // console.log('✅ Jogador registrado no torneio');

    console.log('🎉 Seed concluído!');

  } catch (error) {
    console.error('❌ Erro no seed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
