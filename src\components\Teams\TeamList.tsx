import React, { useState, useEffect } from 'react';
import { Users, Crown, Calendar, GamepadIcon } from 'lucide-react';
import { apiService } from '../../services/apiService';

interface Team {
  id: string;
  name: string;
  game: string;
  logo?: string;
  captainId: string;
  captain: {
    id: string;
    name: string;
    email: string;
  };
  members: Array<{
    id: string;
    name: string;
    email: string;
  }>;
  memberCount: number;
  createdAt: string;
}

interface TeamListProps {
  selectedGame: string;
  onTeamSelect?: (team: Team) => void;
}

export function TeamList({ selectedGame, onTeamSelect }: TeamListProps) {
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadTeams();
  }, [selectedGame]);

  const loadTeams = async () => {
    if (!selectedGame) return;
    
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getTeamsByGame(selectedGame);
      
      if (response.success) {
        setTeams(response.teams || []);
      } else {
        setError(response.error || 'Erro ao carregar times');
      }
    } catch (err) {
      setError('Erro ao carregar times');
      console.error('Erro ao carregar times:', err);
    } finally {
      setLoading(false);
    }
  };

  if (!selectedGame) {
    return (
      <div className="text-center py-12">
        <GamepadIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-400 text-lg">Selecione um jogo para ver os times</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-gray-400">Carregando times...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400 mb-4">{error}</p>
        <button
          onClick={loadTeams}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Tentar Novamente
        </button>
      </div>
    );
  }

  if (teams.length === 0) {
    return (
      <div className="text-center py-12">
        <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-400 text-lg mb-2">Nenhum time encontrado para {selectedGame}</p>
        <p className="text-gray-500 text-sm">Seja o primeiro a criar um time!</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-white">
          Times de {selectedGame}
        </h3>
        <span className="text-gray-400 text-sm">
          {teams.length} time{teams.length !== 1 ? 's' : ''} encontrado{teams.length !== 1 ? 's' : ''}
        </span>
      </div>

      <div className="grid gap-4">
        {teams.map((team) => (
          <div
            key={team.id}
            className={`bg-gray-800 rounded-xl p-6 border border-gray-700 hover:border-purple-500 transition-all duration-200 ${
              onTeamSelect ? 'cursor-pointer hover:bg-gray-750' : ''
            }`}
            onClick={() => onTeamSelect?.(team)}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-4">
                {team.logo ? (
                  <img
                    src={team.logo}
                    alt={team.name}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                ) : (
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                )}
                
                <div>
                  <h4 className="text-lg font-semibold text-white mb-1">
                    {team.name}
                  </h4>
                  <div className="flex items-center space-x-4 text-sm text-gray-400">
                    <div className="flex items-center space-x-1">
                      <Crown className="w-4 h-4" />
                      <span>{team.captain.name}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span>{team.memberCount} membro{team.memberCount !== 1 ? 's' : ''}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-right">
                <div className="text-xs text-gray-500 mb-1">Criado em</div>
                <div className="text-sm text-gray-400">
                  {new Date(team.createdAt).toLocaleDateString('pt-BR')}
                </div>
              </div>
            </div>

            {team.members.length > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-700">
                <div className="text-sm text-gray-400 mb-2">Membros:</div>
                <div className="flex flex-wrap gap-2">
                  {team.members.map((member) => (
                    <span
                      key={member.id}
                      className={`px-3 py-1 rounded-full text-xs ${
                        member.id === team.captainId
                          ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                          : 'bg-gray-700 text-gray-300'
                      }`}
                    >
                      {member.name}
                      {member.id === team.captainId && ' (Capitão)'}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
