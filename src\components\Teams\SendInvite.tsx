import React, { useState } from 'react';
import { Send, User, AlertCircle, CheckCircle } from 'lucide-react';
import { apiService } from '../../services/apiService';

interface SendInviteProps {
  teamId: string;
  teamName: string;
  onInviteSent?: () => void;
  onCancel?: () => void;
}

export function SendInvite({ teamId, teamName, onInviteSent, onCancel }: SendInviteProps) {
  const [playerEmail, setPlayerEmail] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!playerEmail.trim()) {
      setError('Email do jogador é obrigatório');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Primeiro, buscar o usuário pelo email
      const userResponse = await apiService.searchUserByEmail(playerEmail.trim());

      if (!userResponse.success) {
        setError(userResponse.error || 'Jogador não encontrado');
        return;
      }

      // Agora enviar o convite com o ID do jogador
      const inviteResponse = await apiService.sendTeamInvite(
        teamId,
        userResponse.user.id,
        message.trim() || undefined
      );

      if (inviteResponse.success) {
        setSuccess(true);
        setTimeout(() => {
          onInviteSent?.();
        }, 2000);
      } else {
        setError(inviteResponse.error || 'Erro ao enviar convite');
      }
    } catch (err) {
      setError('Erro ao enviar convite. Tente novamente.');
      console.error('Erro ao enviar convite:', err);
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="bg-gray-800 rounded-xl p-8 border border-gray-700">
        <div className="text-center">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">Convite Enviado!</h3>
          <p className="text-gray-400 mb-4">
            O convite foi enviado com sucesso para o jogador.
          </p>
          <p className="text-sm text-gray-500">
            O jogador receberá uma notificação e poderá aceitar ou recusar o convite.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-xl p-8 border border-gray-700">
      <div className="flex items-center space-x-3 mb-6">
        <Send className="w-6 h-6 text-purple-500" />
        <h3 className="text-xl font-semibold text-white">Convidar Jogador</h3>
      </div>

      <div className="mb-6 p-4 bg-gray-700 rounded-lg">
        <h4 className="text-lg font-medium text-white mb-2">Time: {teamName}</h4>
        <p className="text-gray-400 text-sm">
          Convide um jogador para se juntar ao seu time. Apenas jogadores que não estão em outro time do mesmo jogo podem aceitar o convite.
        </p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg flex items-center space-x-3">
          <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="playerEmail" className="block text-sm font-medium text-gray-300 mb-2">
            Email do Jogador *
          </label>
          <div className="relative">
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="email"
              id="playerEmail"
              value={playerEmail}
              onChange={(e) => {
                setPlayerEmail(e.target.value);
                if (error) setError(null);
              }}
              placeholder="<EMAIL>"
              className="w-full pl-10 pr-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              disabled={loading}
              required
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Digite o email do jogador que você deseja convidar
          </p>
        </div>

        <div>
          <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
            Mensagem Personalizada
          </label>
          <textarea
            id="message"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder={`Olá! Gostaria de convidar você para se juntar ao time ${teamName}. Vamos jogar juntos!`}
            rows={4}
            className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
            disabled={loading}
            maxLength={500}
          />
          <p className="text-xs text-gray-500 mt-1">
            {message.length}/500 caracteres (opcional)
          </p>
        </div>

        <div className="flex space-x-4 pt-4">
          <button
            type="submit"
            disabled={loading || !playerEmail.trim()}
            className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            {loading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Enviando...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-2">
                <Send className="w-4 h-4" />
                <span>Enviar Convite</span>
              </div>
            )}
          </button>

          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              disabled={loading}
              className="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 transition-colors"
            >
              Cancelar
            </button>
          )}
        </div>
      </form>

      <div className="mt-6 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
        <h4 className="text-sm font-medium text-yellow-400 mb-2">⚠️ Importante:</h4>
        <ul className="text-xs text-yellow-300 space-y-1">
          <li>• O jogador deve estar registrado na plataforma</li>
          <li>• Jogadores só podem estar em um time por jogo</li>
          <li>• O convite expira em 7 dias se não for respondido</li>
          <li>• Você pode cancelar convites pendentes a qualquer momento</li>
        </ul>
      </div>
    </div>
  );
}
