import express from 'express';
import { prisma } from '../lib/prisma';
import { authenticateToken, requireRole, AuthRequest } from '../middleware/auth';

const router = express.Router();

// GET /api/tournaments - Listar todos os torneios
router.get('/', async (req, res) => {
  try {
    const tournaments = await prisma.tournament.findMany({
      include: {
        organizer: {
          select: {
            id: true,
            name: true,
            email: true,
            company: true,
            website: true,
            socialMedia: true,
            bio: true,
            avatar: true
          }
        },
        prizeDistributions: true,
        registrations: true,
        _count: {
          select: { registrations: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    const formattedTournaments = tournaments.map(tournament => ({
      id: tournament.id,
      name: tournament.name,
      game: tournament.game,
      organizerId: tournament.organizerId,
      description: tournament.description || '',
      format: tournament.format.toLowerCase().replace('_', '-'),
      maxParticipants: tournament.maxParticipants,
      registrationFee: Number(tournament.registrationFee),
      prizePool: Number(tournament.prizePool),
      prizeDistribution: tournament.prizeDistributions.map(pd => ({
        place: pd.place,
        percentage: Number(pd.percentage)
      })),
      rules: tournament.rules,
      platforms: tournament.platforms,
      startDate: tournament.startDate.toISOString(),
      endDate: tournament.endDate.toISOString(),
      registrationDeadline: tournament.registrationDeadline.toISOString(),
      status: tournament.status.toLowerCase(),
      participants: tournament.registrations.map(r => r.participantId),
      participantCount: tournament._count.registrations,
      createdAt: tournament.createdAt.toISOString()
    }));

    res.json({
      success: true,
      tournaments: formattedTournaments
    });

  } catch (error) {
    console.error('Erro ao buscar torneios:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar torneios'
    });
  }
});

// POST /api/tournaments - Criar novo torneio
router.post('/', authenticateToken, requireRole(['ORGANIZER']), async (req: AuthRequest, res) => {
  try {
    const {
      name,
      game,
      description,
      format,
      maxParticipants,
      registrationFee,
      prizePool,
      prizeDistribution,
      rules,
      platforms,
      startDate,
      endDate,
      registrationDeadline
    } = req.body;

    // Validações
    if (!name || !game || !format || !maxParticipants || !startDate || !endDate || !registrationDeadline) {
      return res.status(400).json({
        success: false,
        error: 'Campos obrigatórios não preenchidos'
      });
    }

    const tournament = await prisma.tournament.create({
      data: {
        name,
        game,
        organizerId: req.user!.id,
        description: description || '',
        format: format.toUpperCase().replace('-', '_'),
        maxParticipants: parseInt(maxParticipants),
        registrationFee: parseFloat(registrationFee) || 0,
        prizePool: parseFloat(prizePool) || 0,
        rules: Array.isArray(rules) ? rules.filter(rule => rule.trim() !== '') : [],
        platforms: Array.isArray(platforms) ? platforms.filter(platform => platform.trim() !== '') : [],
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        registrationDeadline: new Date(registrationDeadline),
        status: 'UPCOMING',
        prizeDistributions: {
          create: Array.isArray(prizeDistribution) ? prizeDistribution.map(pd => ({
            place: parseInt(pd.place),
            percentage: parseFloat(pd.percentage)
          })) : []
        }
      },
      include: {
        organizer: {
          select: {
            id: true,
            name: true,
            email: true,
            company: true,
            website: true,
            socialMedia: true,
            bio: true,
            avatar: true
          }
        },
        prizeDistributions: true,
        registrations: true
      }
    });

    const formattedTournament = {
      id: tournament.id,
      name: tournament.name,
      game: tournament.game,
      organizerId: tournament.organizerId,
      description: tournament.description || '',
      format: tournament.format.toLowerCase().replace('_', '-'),
      maxParticipants: tournament.maxParticipants,
      registrationFee: Number(tournament.registrationFee),
      prizePool: Number(tournament.prizePool),
      prizeDistribution: tournament.prizeDistributions.map(pd => ({
        place: pd.place,
        percentage: Number(pd.percentage)
      })),
      rules: tournament.rules,
      platforms: tournament.platforms,
      startDate: tournament.startDate.toISOString(),
      endDate: tournament.endDate.toISOString(),
      registrationDeadline: tournament.registrationDeadline.toISOString(),
      status: tournament.status.toLowerCase(),
      participants: [],
      createdAt: tournament.createdAt.toISOString()
    };

    res.status(201).json({
      success: true,
      tournament: formattedTournament
    });

  } catch (error) {
    console.error('Erro ao criar torneio:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao criar torneio'
    });
  }
});

// POST /api/tournaments/:tournamentId/register - Registrar em torneio
router.post('/:tournamentId/register', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { tournamentId } = req.params;
    const { participantType = 'player', teamId } = req.body;
    const userId = req.user!.id;

    // Verificar se o torneio existe
    const tournament = await prisma.tournament.findUnique({
      where: { id: tournamentId },
      include: { registrations: true }
    });

    if (!tournament) {
      return res.status(404).json({
        success: false,
        error: 'Torneio não encontrado'
      });
    }

    if (tournament.status !== 'REGISTRATION') {
      return res.status(400).json({
        success: false,
        error: 'Torneio não está aberto para inscrições'
      });
    }

    if (tournament.registrations.length >= tournament.maxParticipants) {
      return res.status(400).json({
        success: false,
        error: 'Torneio lotado'
      });
    }

    let participantId: string;
    let finalParticipantType: 'PLAYER' | 'TEAM';

    if (participantType === 'team') {
      if (!teamId) {
        return res.status(400).json({
          success: false,
          error: 'ID do time é obrigatório para inscrição em equipe'
        });
      }

      // Verificar se o time existe e é do jogo correto
      const team = await prisma.team.findUnique({
        where: { id: teamId },
        include: {
          members: true,
          captain: true
        }
      });

      if (!team) {
        return res.status(404).json({
          success: false,
          error: 'Time não encontrado'
        });
      }

      if (team.game !== tournament.game) {
        return res.status(400).json({
          success: false,
          error: `Este time é de ${team.game}, mas o torneio é de ${tournament.game}`
        });
      }

      // Verificar se o usuário é membro do time
      const isMember = team.members.some(member => member.playerId === userId);
      if (!isMember) {
        return res.status(403).json({
          success: false,
          error: 'Você não é membro deste time'
        });
      }

      // Verificar se o usuário é o capitão (apenas capitão pode inscrever o time)
      if (team.captainId !== userId) {
        return res.status(403).json({
          success: false,
          error: 'Apenas o capitão pode inscrever o time em torneios'
        });
      }

      participantId = teamId;
      finalParticipantType = 'TEAM';
    } else {
      participantId = userId;
      finalParticipantType = 'PLAYER';
    }

    // Verificar se já está inscrito
    const existingRegistration = await prisma.tournamentRegistration.findUnique({
      where: {
        tournamentId_participantId: {
          tournamentId,
          participantId
        }
      }
    });

    if (existingRegistration) {
      return res.status(400).json({
        success: false,
        error: 'Já inscrito neste torneio'
      });
    }

    // Criar inscrição
    await prisma.tournamentRegistration.create({
      data: {
        tournamentId,
        participantId,
        participantType: finalParticipantType
      }
    });

    res.json({
      success: true,
      message: `Inscrição realizada com sucesso ${finalParticipantType === 'TEAM' ? 'para o time' : 'como jogador individual'}`
    });

  } catch (error) {
    console.error('Erro ao registrar no torneio:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao registrar no torneio'
    });
  }
});

// GET /api/tournaments/my - Torneios do usuário logado
router.get('/my', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const userId = req.user!.id;
    const userType = req.user!.type;

    let tournaments;

    if (userType === 'ORGANIZER') {
      // Torneios organizados pelo usuário
      tournaments = await prisma.tournament.findMany({
        where: { organizerId: userId },
        include: {
          organizer: {
            select: {
              id: true,
              name: true,
              email: true,
              company: true,
              website: true,
              socialMedia: true,
              bio: true,
              avatar: true
            }
          },
          prizeDistributions: true,
          registrations: true,
          _count: { select: { registrations: true } }
        },
        orderBy: { createdAt: 'desc' }
      });
    } else {
      // Torneios em que o jogador está inscrito
      const registrations = await prisma.tournamentRegistration.findMany({
        where: { participantId: userId },
        include: {
          tournament: {
            include: {
              organizer: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  company: true,
                  website: true,
                  socialMedia: true,
                  bio: true,
                  avatar: true
                }
              },
              prizeDistributions: true,
              registrations: true,
              _count: { select: { registrations: true } }
            }
          }
        }
      });
      tournaments = registrations.map(reg => reg.tournament);
    }

    const formattedTournaments = tournaments.map(tournament => ({
      id: tournament.id,
      name: tournament.name,
      game: tournament.game,
      organizerId: tournament.organizerId,
      description: tournament.description || '',
      format: tournament.format.toLowerCase().replace('_', '-'),
      maxParticipants: tournament.maxParticipants,
      registrationFee: Number(tournament.registrationFee),
      prizePool: Number(tournament.prizePool),
      prizeDistribution: tournament.prizeDistributions.map(pd => ({
        place: pd.place,
        percentage: Number(pd.percentage)
      })),
      rules: tournament.rules,
      platforms: tournament.platforms,
      startDate: tournament.startDate.toISOString(),
      endDate: tournament.endDate.toISOString(),
      registrationDeadline: tournament.registrationDeadline.toISOString(),
      status: tournament.status.toLowerCase(),
      participants: tournament.registrations.map(r => r.participantId),
      participantCount: tournament._count.registrations,
      createdAt: tournament.createdAt.toISOString()
    }));

    res.json({
      success: true,
      tournaments: formattedTournaments
    });

  } catch (error) {
    console.error('Erro ao buscar torneios do usuário:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar torneios'
    });
  }
});

export { router as tournamentRoutes };
