import React, { useState, useEffect } from 'react';
import { Mail, Users, Crown, Check, X, Clock, AlertCircle } from 'lucide-react';
import { apiService } from '../../services/apiService';
import { useToast } from '../../contexts/ToastContext';

interface TeamInvite {
  id: string;
  team: {
    id: string;
    name: string;
    game: string;
    logo?: string;
    captain: {
      id: string;
      name: string;
      email: string;
    };
    memberCount: number;
  };
  inviter: {
    id: string;
    name: string;
    email: string;
  };
  message: string;
  createdAt: string;
}

export function TeamInvites() {
  const [invites, setInvites] = useState<TeamInvite[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [respondingTo, setRespondingTo] = useState<string | null>(null);
  const toast = useToast();

  useEffect(() => {
    loadInvites();
  }, []);

  const loadInvites = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getReceivedInvites();
      
      if (response.success) {
        setInvites(response.invites || []);
      } else {
        setError(response.error || 'Erro ao carregar convites');
      }
    } catch (err) {
      setError('Erro ao carregar convites');
      console.error('Erro ao carregar convites:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleResponse = async (inviteId: string, response: 'ACCEPTED' | 'DECLINED') => {
    try {
      setRespondingTo(inviteId);
      const result = await apiService.respondToInvite(inviteId, response);
      
      if (result.success) {
        // Remover o convite da lista
        setInvites(prev => prev.filter(invite => invite.id !== inviteId));

        // Mostrar mensagem de sucesso
        if (response === 'ACCEPTED') {
          toast.success('Convite aceito! Você foi adicionado ao time com sucesso!');
        } else {
          toast.info('Convite recusado.');
        }
      } else {
        const errorMsg = result.error || 'Erro ao responder convite';
        setError(errorMsg);
        toast.error(errorMsg);
      }
    } catch (err) {
      const errorMsg = 'Erro ao responder convite';
      setError(errorMsg);
      toast.error(errorMsg);
      console.error('Erro ao responder convite:', err);
    } finally {
      setRespondingTo(null);
    }
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-gray-400">Carregando convites...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
        <p className="text-red-400 mb-4">{error}</p>
        <button
          onClick={loadInvites}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Tentar Novamente
        </button>
      </div>
    );
  }

  if (invites.length === 0) {
    return (
      <div className="text-center py-12">
        <Mail className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">Nenhum convite pendente</h3>
        <p className="text-gray-400">
          Você não possui convites de times no momento
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-semibold text-white">Convites Recebidos</h3>
        <span className="text-gray-400 text-sm">
          {invites.length} convite{invites.length !== 1 ? 's' : ''} pendente{invites.length !== 1 ? 's' : ''}
        </span>
      </div>

      <div className="space-y-4">
        {invites.map((invite) => (
          <div
            key={invite.id}
            className="bg-gray-800 rounded-xl p-6 border border-gray-700 hover:border-purple-500/50 transition-all duration-200"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-4">
                {invite.team.logo ? (
                  <img
                    src={invite.team.logo}
                    alt={invite.team.name}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                ) : (
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                )}
                
                <div>
                  <h4 className="text-lg font-semibold text-white mb-1">
                    {invite.team.name}
                  </h4>
                  <div className="flex items-center space-x-4 text-sm text-gray-400">
                    <span>{invite.team.game}</span>
                    <div className="flex items-center space-x-1">
                      <Crown className="w-4 h-4" />
                      <span>{invite.team.captain.name}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span>{invite.team.memberCount} membro{invite.team.memberCount !== 1 ? 's' : ''}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-right text-sm text-gray-500">
                <div className="flex items-center space-x-1 mb-1">
                  <Clock className="w-4 h-4" />
                  <span>{new Date(invite.createdAt).toLocaleDateString('pt-BR')}</span>
                </div>
                <div>Convidado por {invite.inviter.name}</div>
              </div>
            </div>

            {invite.message && (
              <div className="mb-4 p-3 bg-gray-700 rounded-lg">
                <p className="text-gray-300 text-sm italic">"{invite.message}"</p>
              </div>
            )}

            <div className="flex items-center justify-between pt-4 border-t border-gray-700">
              <div className="text-sm text-gray-400">
                Responda ao convite para se juntar ao time
              </div>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => handleResponse(invite.id, 'DECLINED')}
                  disabled={respondingTo === invite.id}
                  className="flex items-center space-x-2 px-4 py-2 border border-red-500 text-red-400 rounded-lg hover:bg-red-500/10 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 transition-colors"
                >
                  {respondingTo === invite.id ? (
                    <div className="w-4 h-4 border-2 border-red-400 border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <X className="w-4 h-4" />
                  )}
                  <span>Recusar</span>
                </button>

                <button
                  onClick={() => handleResponse(invite.id, 'ACCEPTED')}
                  disabled={respondingTo === invite.id}
                  className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 transition-all duration-200"
                >
                  {respondingTo === invite.id ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <Check className="w-4 h-4" />
                  )}
                  <span>Aceitar</span>
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
        <h4 className="text-sm font-medium text-blue-400 mb-2">💡 Dicas sobre Convites:</h4>
        <ul className="text-xs text-blue-300 space-y-1">
          <li>• Você só pode estar em um time por jogo</li>
          <li>• Aceitar um convite o adicionará automaticamente ao time</li>
          <li>• Convites expiram automaticamente após 7 dias</li>
          <li>• Você pode recusar convites sem penalidades</li>
        </ul>
      </div>
    </div>
  );
}
