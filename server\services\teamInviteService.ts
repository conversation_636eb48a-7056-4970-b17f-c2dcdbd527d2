import { prisma } from '../lib/prisma';
import { TeamService } from './teamService';

export class TeamInviteService {
  // Enviar convite para jogador
  static async sendInvite(teamId: string, playerId: string, invitedBy: string, message?: string) {
    try {
      // Verificar se o time existe
      const team = await prisma.team.findUnique({
        where: { id: teamId },
        include: { captain: true }
      });

      if (!team) {
        throw new Error('Time não encontrado');
      }

      // Verificar se quem está convidando é o capitão
      if (team.captainId !== invitedBy) {
        throw new Error('Apenas o capitão pode enviar convites');
      }

      // Verificar se o jogador existe e é do tipo PLAYER
      const player = await prisma.user.findUnique({
        where: { id: playerId }
      });

      if (!player) {
        throw new Error('Jogador não encontrado');
      }

      if (player.type !== 'PLAYER') {
        throw new Error('Apenas jogadores podem ser convidados para times');
      }

      // Verificar se o jogador já está em um time para este jogo
      const existingTeam = await TeamService.isPlayerInTeamForGame(playerId, team.game);
      if (existingTeam.inTeam) {
        throw new Error(`Jogador já está no time "${existingTeam.teamName}" para ${team.game}`);
      }

      // Verificar se já existe um convite pendente
      const existingInvite = await prisma.teamInvite.findUnique({
        where: {
          teamId_playerId: {
            teamId,
            playerId
          }
        }
      });

      if (existingInvite && existingInvite.status === 'PENDING') {
        throw new Error('Já existe um convite pendente para este jogador');
      }

      // Criar o convite
      const invite = await prisma.teamInvite.create({
        data: {
          teamId,
          playerId,
          invitedBy,
          message: message || `Você foi convidado para se juntar ao time ${team.name}!`,
          status: 'PENDING'
        },
        include: {
          team: {
            select: { id: true, name: true, game: true, logo: true }
          },
          player: {
            select: { id: true, name: true, email: true }
          },
          inviter: {
            select: { id: true, name: true, email: true }
          }
        }
      });

      return {
        success: true,
        invite: {
          id: invite.id,
          team: invite.team,
          player: invite.player,
          inviter: invite.inviter,
          message: invite.message,
          status: invite.status,
          createdAt: invite.createdAt
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro ao enviar convite'
      };
    }
  }

  // Responder a um convite (aceitar/recusar)
  static async respondToInvite(inviteId: string, playerId: string, response: 'ACCEPTED' | 'DECLINED') {
    try {
      // Buscar o convite
      const invite = await prisma.teamInvite.findUnique({
        where: { id: inviteId },
        include: {
          team: true,
          player: true
        }
      });

      if (!invite) {
        throw new Error('Convite não encontrado');
      }

      // Verificar se é o jogador correto
      if (invite.playerId !== playerId) {
        throw new Error('Você não pode responder a este convite');
      }

      // Verificar se o convite ainda está pendente
      if (invite.status !== 'PENDING') {
        throw new Error('Este convite já foi respondido');
      }

      // Se aceitar, verificar se ainda pode entrar no time
      if (response === 'ACCEPTED') {
        // Verificar se o jogador já está em um time para este jogo
        const existingTeam = await TeamService.isPlayerInTeamForGame(playerId, invite.team.game);
        if (existingTeam.inTeam) {
          throw new Error(`Você já está no time "${existingTeam.teamName}" para ${invite.team.game}`);
        }

        // Verificar se o time ainda tem vagas (assumindo máximo de 5 membros)
        const teamMembers = await prisma.teamMember.count({
          where: { teamId: invite.teamId }
        });

        if (teamMembers >= 5) {
          throw new Error('O time está lotado');
        }

        // Aceitar convite e adicionar ao time
        await prisma.$transaction([
          // Atualizar status do convite
          prisma.teamInvite.update({
            where: { id: inviteId },
            data: {
              status: response,
              respondedAt: new Date()
            }
          }),
          // Adicionar jogador ao time
          prisma.teamMember.create({
            data: {
              teamId: invite.teamId,
              playerId: playerId
            }
          })
        ]);

        return {
          success: true,
          message: `Você se juntou ao time ${invite.team.name} com sucesso!`
        };
      } else {
        // Recusar convite
        await prisma.teamInvite.update({
          where: { id: inviteId },
          data: {
            status: response,
            respondedAt: new Date()
          }
        });

        return {
          success: true,
          message: 'Convite recusado'
        };
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro ao responder convite'
      };
    }
  }

  // Buscar convites recebidos por um jogador
  static async getReceivedInvites(playerId: string) {
    try {
      const invites = await prisma.teamInvite.findMany({
        where: { 
          playerId,
          status: 'PENDING'
        },
        include: {
          team: {
            include: {
              captain: {
                select: { id: true, name: true, email: true }
              },
              members: {
                include: {
                  player: {
                    select: { id: true, name: true, email: true }
                  }
                }
              }
            }
          },
          inviter: {
            select: { id: true, name: true, email: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      return invites.map(invite => ({
        id: invite.id,
        team: {
          id: invite.team.id,
          name: invite.team.name,
          game: invite.team.game,
          logo: invite.team.logo,
          captain: invite.team.captain,
          memberCount: invite.team.members.length
        },
        inviter: invite.inviter,
        message: invite.message,
        createdAt: invite.createdAt
      }));

    } catch (error) {
      console.error('Erro ao buscar convites recebidos:', error);
      return [];
    }
  }

  // Buscar convites enviados por um time
  static async getSentInvites(teamId: string) {
    try {
      const invites = await prisma.teamInvite.findMany({
        where: { teamId },
        include: {
          player: {
            select: { id: true, name: true, email: true }
          },
          inviter: {
            select: { id: true, name: true, email: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      return invites.map(invite => ({
        id: invite.id,
        player: invite.player,
        inviter: invite.inviter,
        message: invite.message,
        status: invite.status,
        createdAt: invite.createdAt,
        respondedAt: invite.respondedAt
      }));

    } catch (error) {
      console.error('Erro ao buscar convites enviados:', error);
      return [];
    }
  }

  // Cancelar convite (apenas o capitão que enviou)
  static async cancelInvite(inviteId: string, userId: string) {
    try {
      const invite = await prisma.teamInvite.findUnique({
        where: { id: inviteId },
        include: { team: true }
      });

      if (!invite) {
        throw new Error('Convite não encontrado');
      }

      // Verificar se é o capitão do time
      if (invite.team.captainId !== userId) {
        throw new Error('Apenas o capitão pode cancelar convites');
      }

      if (invite.status !== 'PENDING') {
        throw new Error('Apenas convites pendentes podem ser cancelados');
      }

      await prisma.teamInvite.delete({
        where: { id: inviteId }
      });

      return {
        success: true,
        message: 'Convite cancelado com sucesso'
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro ao cancelar convite'
      };
    }
  }
}
