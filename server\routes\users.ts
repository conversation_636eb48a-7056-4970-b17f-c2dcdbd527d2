import express from 'express';
import { prisma } from '../lib/prisma';
import { authenticateToken, AuthRequest } from '../middleware/auth';

const router = express.Router();

// GET /api/users/profile - Perfil do usuário logado (REMOVIDO - usando a versão para organizadores abaixo)

// PUT /api/users/profile - Atualizar perfil (REMOVIDO - usando a versão para organizadores abaixo)

// GET /api/users/stats - Estatísticas do usuário
router.get('/stats', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const userId = req.user!.id;
    const userType = req.user!.type;

    let stats;

    if (userType === 'ORGANIZER') {
      const tournaments = await prisma.tournament.findMany({
        where: { organizerId: userId },
        include: {
          registrations: true
        }
      });

      stats = {
        totalTournaments: tournaments.length,
        activeTournaments: tournaments.filter(t => ['UPCOMING', 'REGISTRATION', 'ONGOING'].includes(t.status)).length,
        completedTournaments: tournaments.filter(t => t.status === 'COMPLETED').length,
        totalParticipants: tournaments.reduce((sum, t) => sum + t.registrations.length, 0),
        totalPrizePool: tournaments.reduce((sum, t) => sum + Number(t.prizePool), 0)
      };
    } else {
      const registrations = await prisma.tournamentRegistration.findMany({
        where: { participantId: userId },
        include: {
          tournament: true
        }
      });

      const achievements = await prisma.achievement.findMany({
        where: { userId }
      });

      stats = {
        totalTournaments: registrations.length,
        activeTournaments: registrations.filter(r => ['UPCOMING', 'REGISTRATION', 'ONGOING'].includes(r.tournament.status)).length,
        completedTournaments: registrations.filter(r => r.tournament.status === 'COMPLETED').length,
        achievements: achievements.length,
        totalPrizeWon: achievements.reduce((sum, a) => sum + Number(a.prize || 0), 0)
      };
    }

    res.json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar estatísticas'
    });
  }
});

// GET /api/users/profile - Obter perfil do usuário logado
router.get('/profile', authenticateToken, async (req: AuthRequest, res) => {
  try {
    console.log('🔍 GET /api/users/profile chamado');
    console.log('👤 Usuário:', req.user?.id, req.user?.name);

    const user = await prisma.user.findUnique({
      where: { id: req.user!.id },
      select: {
        id: true,
        name: true,
        email: true,
        type: true,
        avatar: true,
        company: true,
        website: true,
        socialMedia: true,
        bio: true,
        createdAt: true
      }
    });

    console.log('📊 Dados encontrados no banco:', user);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'Usuário não encontrado'
      });
    }

    console.log('✅ Enviando resposta:', { success: true, user });

    res.json({
      success: true,
      user
    });

  } catch (error) {
    console.error('❌ Erro ao buscar perfil:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar perfil'
    });
  }
});

// PUT /api/users/profile - Atualizar perfil do usuário
router.put('/profile', authenticateToken, async (req: AuthRequest, res) => {
  try {
    console.log('🔧 PUT /api/users/profile chamado');
    console.log('📝 Dados recebidos:', req.body);
    console.log('👤 Usuário:', req.user?.id, req.user?.name);

    const { company, website, socialMedia, bio } = req.body;

    console.log('💾 Atualizando usuário no banco...');
    const updatedUser = await prisma.user.update({
      where: { id: req.user!.id },
      data: {
        company,
        website,
        socialMedia,
        bio
      },
      select: {
        id: true,
        name: true,
        email: true,
        type: true,
        avatar: true,
        company: true,
        website: true,
        socialMedia: true,
        bio: true
      }
    });

    console.log('✅ Usuário atualizado:', updatedUser);

    res.json({
      success: true,
      user: updatedUser
    });

  } catch (error) {
    console.error('❌ Erro ao atualizar perfil:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao atualizar perfil'
    });
  }
});

// GET /api/users/search - Buscar usuário por email
router.get('/search', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { email } = req.query;

    if (!email || typeof email !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Email é obrigatório'
      });
    }

    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
      select: {
        id: true,
        name: true,
        email: true,
        type: true
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'Usuário não encontrado'
      });
    }

    if (user.type !== 'PLAYER') {
      return res.status(400).json({
        success: false,
        error: 'Apenas jogadores podem ser convidados para times'
      });
    }

    res.json({
      success: true,
      user
    });

  } catch (error) {
    console.error('Erro ao buscar usuário:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar usuário'
    });
  }
});

export { router as userRoutes };
