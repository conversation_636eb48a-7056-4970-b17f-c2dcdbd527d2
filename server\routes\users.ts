import express from 'express';
import { prisma } from '../lib/prisma';
import { authenticateToken, AuthRequest } from '../middleware/auth';

const router = express.Router();

// GET /api/users/profile - Perfil do usuário logado
router.get('/profile', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user!.id },
      include: {
        playerProfile: true,
        organizedTournaments: {
          select: {
            id: true,
            name: true,
            status: true,
            createdAt: true
          }
        },
        tournamentRegistrations: {
          include: {
            tournament: {
              select: {
                id: true,
                name: true,
                status: true,
                createdAt: true
              }
            }
          }
        }
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'Usuário não encontrado'
      });
    }

    const profile = {
      id: user.id,
      email: user.email,
      name: user.name,
      type: user.type.toLowerCase(),
      avatar: user.avatar,
      createdAt: user.createdAt.toISOString(),
      playerProfile: user.playerProfile ? {
        age: user.playerProfile.age,
        peripherals: user.playerProfile.peripherals,
        preferredGames: user.playerProfile.preferredGames,
        rank: user.playerProfile.rank,
        twitchUrl: user.playerProfile.twitchUrl,
        youtubeUrl: user.playerProfile.youtubeUrl,
        discordTag: user.playerProfile.discordTag
      } : null,
      stats: {
        organizedTournaments: user.organizedTournaments.length,
        participatedTournaments: user.tournamentRegistrations.length,
        activeTournaments: user.type === 'ORGANIZER' 
          ? user.organizedTournaments.filter(t => ['UPCOMING', 'REGISTRATION', 'ONGOING'].includes(t.status)).length
          : user.tournamentRegistrations.filter(r => ['UPCOMING', 'REGISTRATION', 'ONGOING'].includes(r.tournament.status)).length
      }
    };

    res.json({
      success: true,
      profile
    });

  } catch (error) {
    console.error('Erro ao buscar perfil:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar perfil'
    });
  }
});

// PUT /api/users/profile - Atualizar perfil
router.put('/profile', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { name, avatar, playerProfile } = req.body;
    const userId = req.user!.id;

    // Atualizar dados básicos do usuário
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        ...(name && { name }),
        ...(avatar && { avatar })
      }
    });

    // Se for jogador e há dados do perfil, atualizar
    if (req.user!.type === 'PLAYER' && playerProfile) {
      await prisma.playerProfile.upsert({
        where: { userId },
        update: {
          ...(playerProfile.age && { age: parseInt(playerProfile.age) }),
          ...(playerProfile.peripherals && { peripherals: playerProfile.peripherals }),
          ...(playerProfile.preferredGames && { preferredGames: playerProfile.preferredGames }),
          ...(playerProfile.rank && { rank: playerProfile.rank }),
          ...(playerProfile.twitchUrl && { twitchUrl: playerProfile.twitchUrl }),
          ...(playerProfile.youtubeUrl && { youtubeUrl: playerProfile.youtubeUrl }),
          ...(playerProfile.discordTag && { discordTag: playerProfile.discordTag })
        },
        create: {
          userId,
          age: playerProfile.age ? parseInt(playerProfile.age) : null,
          peripherals: playerProfile.peripherals || null,
          preferredGames: playerProfile.preferredGames || [],
          rank: playerProfile.rank || null,
          twitchUrl: playerProfile.twitchUrl || null,
          youtubeUrl: playerProfile.youtubeUrl || null,
          discordTag: playerProfile.discordTag || null
        }
      });
    }

    res.json({
      success: true,
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        type: updatedUser.type.toLowerCase(),
        avatar: updatedUser.avatar,
        createdAt: updatedUser.createdAt.toISOString()
      }
    });

  } catch (error) {
    console.error('Erro ao atualizar perfil:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao atualizar perfil'
    });
  }
});

// GET /api/users/stats - Estatísticas do usuário
router.get('/stats', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const userId = req.user!.id;
    const userType = req.user!.type;

    let stats;

    if (userType === 'ORGANIZER') {
      const tournaments = await prisma.tournament.findMany({
        where: { organizerId: userId },
        include: {
          registrations: true
        }
      });

      stats = {
        totalTournaments: tournaments.length,
        activeTournaments: tournaments.filter(t => ['UPCOMING', 'REGISTRATION', 'ONGOING'].includes(t.status)).length,
        completedTournaments: tournaments.filter(t => t.status === 'COMPLETED').length,
        totalParticipants: tournaments.reduce((sum, t) => sum + t.registrations.length, 0),
        totalPrizePool: tournaments.reduce((sum, t) => sum + Number(t.prizePool), 0)
      };
    } else {
      const registrations = await prisma.tournamentRegistration.findMany({
        where: { participantId: userId },
        include: {
          tournament: true
        }
      });

      const achievements = await prisma.achievement.findMany({
        where: { userId }
      });

      stats = {
        totalTournaments: registrations.length,
        activeTournaments: registrations.filter(r => ['UPCOMING', 'REGISTRATION', 'ONGOING'].includes(r.tournament.status)).length,
        completedTournaments: registrations.filter(r => r.tournament.status === 'COMPLETED').length,
        achievements: achievements.length,
        totalPrizeWon: achievements.reduce((sum, a) => sum + Number(a.prize || 0), 0)
      };
    }

    res.json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar estatísticas'
    });
  }
});

export { router as userRoutes };
