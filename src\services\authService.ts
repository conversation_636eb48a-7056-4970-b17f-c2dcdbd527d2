import { prisma } from '../lib/prisma';
import bcrypt from 'bcryptjs';
import { User, UserType } from '../types';

// Função simples para criar token (sem JWT por enquanto)
function createSimpleToken(userId: string, email: string, type: string): string {
  const payload = { userId, email, type, exp: Date.now() + (7 * 24 * 60 * 60 * 1000) }; // 7 dias
  return btoa(JSON.stringify(payload));
}

// Função para verificar token simples
function verifySimpleToken(token: string): any {
  try {
    const payload = JSON.parse(atob(token));
    if (payload.exp < Date.now()) {
      throw new Error('Token expirado');
    }
    return payload;
  } catch {
    throw new Error('Token inválido');
  }
}

export interface AuthResponse {
  success: boolean;
  user?: User;
  token?: string;
  error?: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  type: UserType;
}

export interface LoginData {
  email: string;
  password: string;
  type: UserType;
}

export class AuthService {
  // Registrar novo usuário
  static async register(data: RegisterData): Promise<AuthResponse> {
    try {
      // Verificar se email já existe
      const existingUser = await prisma.user.findUnique({
        where: { email: data.email }
      });

      if (existingUser) {
        return {
          success: false,
          error: 'Email já está em uso'
        };
      }

      // Hash da senha
      const hashedPassword = await bcrypt.hash(data.password, 10);

      // Criar usuário
      const newUser = await prisma.user.create({
        data: {
          email: data.email,
          password: hashedPassword,
          name: data.name,
          type: data.type === 'player' ? 'PLAYER' : 'ORGANIZER'
        }
      });

      // Se for jogador, criar perfil
      if (data.type === 'player') {
        await prisma.playerProfile.create({
          data: {
            userId: newUser.id,
            preferredGames: []
          }
        });
      }

      // Gerar token
      const token = createSimpleToken(newUser.id, newUser.email, newUser.type);

      // Converter para formato do frontend
      const user: User = {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
        type: newUser.type.toLowerCase() as UserType,
        avatar: newUser.avatar || undefined,
        createdAt: newUser.createdAt.toISOString()
      };

      return {
        success: true,
        user,
        token
      };

    } catch (error) {
      console.error('Erro no registro:', error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }

  // Login do usuário
  static async login(data: LoginData): Promise<AuthResponse> {
    try {
      // Buscar usuário por email e tipo
      const user = await prisma.user.findFirst({
        where: {
          email: data.email,
          type: data.type === 'player' ? 'PLAYER' : 'ORGANIZER'
        },
        include: {
          playerProfile: true
        }
      });

      if (!user) {
        return {
          success: false,
          error: 'Credenciais inválidas'
        };
      }

      // Verificar senha
      const isValidPassword = await bcrypt.compare(data.password, user.password);
      if (!isValidPassword) {
        return {
          success: false,
          error: 'Credenciais inválidas'
        };
      }

      // Gerar token
      const token = createSimpleToken(user.id, user.email, user.type);

      // Converter para formato do frontend
      const userResponse: User = {
        id: user.id,
        email: user.email,
        name: user.name,
        type: user.type.toLowerCase() as UserType,
        avatar: user.avatar || undefined,
        createdAt: user.createdAt.toISOString()
      };

      return {
        success: true,
        user: userResponse,
        token
      };

    } catch (error) {
      console.error('Erro no login:', error);
      return {
        success: false,
        error: 'Erro interno do servidor'
      };
    }
  }

  // Verificar token
  static async verifyToken(token: string): Promise<AuthResponse> {
    try {
      const decoded = verifySimpleToken(token);

      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        include: {
          playerProfile: true
        }
      });

      if (!user) {
        return {
          success: false,
          error: 'Token inválido'
        };
      }

      const userResponse: User = {
        id: user.id,
        email: user.email,
        name: user.name,
        type: user.type.toLowerCase() as UserType,
        avatar: user.avatar || undefined,
        createdAt: user.createdAt.toISOString()
      };

      return {
        success: true,
        user: userResponse,
        token
      };

    } catch (error) {
      return {
        success: false,
        error: 'Token inválido'
      };
    }
  }

  // Atualizar perfil
  static async updateProfile(userId: string, updates: Partial<User>): Promise<AuthResponse> {
    try {
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          name: updates.name,
          avatar: updates.avatar
        }
      });

      const userResponse: User = {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        type: updatedUser.type.toLowerCase() as UserType,
        avatar: updatedUser.avatar || undefined,
        createdAt: updatedUser.createdAt.toISOString()
      };

      return {
        success: true,
        user: userResponse
      };

    } catch (error) {
      console.error('Erro ao atualizar perfil:', error);
      return {
        success: false,
        error: 'Erro ao atualizar perfil'
      };
    }
  }
}
