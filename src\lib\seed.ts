import { prisma } from './prisma';
import bcrypt from 'bcryptjs';

export async function seedDatabase() {
  try {
    console.log('🌱 Iniciando seed do banco de dados...');

    // Limpar dados existentes
    await prisma.tournamentRegistration.deleteMany();
    await prisma.prizeDistribution.deleteMany();
    await prisma.tournament.deleteMany();
    await prisma.playerProfile.deleteMany();
    await prisma.teamMember.deleteMany();
    await prisma.team.deleteMany();
    await prisma.user.deleteMany();

    // Criar usuários
    const hashedPassword = await bcrypt.hash('123456', 10);

    // Organizador
    const organizer = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: '<PERSON>',
        type: 'ORGANIZER'
      }
    });

    // Jogadores
    const player1 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: '<PERSON>',
        type: 'PLAYER',
        playerProfile: {
          create: {
            age: 22,
            preferredGames: ['Counter-Strike 2', 'Valorant'],
            rank: 'Global Elite',
            twitchUrl: 'https://twitch.tv/maria_gamer',
            discordTag: 'Maria#1234'
          }
        }
      }
    });

    const player2 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Pedro Costa',
        type: 'PLAYER',
        playerProfile: {
          create: {
            age: 25,
            preferredGames: ['Counter-Strike 2', 'League of Legends'],
            rank: 'Supreme Master',
            discordTag: 'Pedro#5678'
          }
        }
      }
    });

    const player3 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Ana Oliveira',
        type: 'PLAYER',
        playerProfile: {
          create: {
            age: 20,
            preferredGames: ['Valorant', 'Apex Legends'],
            rank: 'Immortal',
            youtubeUrl: 'https://youtube.com/ana_gaming'
          }
        }
      }
    });

    // Criar time
    const team = await prisma.team.create({
      data: {
        name: 'Thunder Wolves',
        captainId: player1.id,
        members: {
          create: [
            { playerId: player1.id },
            { playerId: player2.id },
            { playerId: player3.id }
          ]
        }
      }
    });

    // Criar torneios
    const tournament1 = await prisma.tournament.create({
      data: {
        name: 'Liga CS2 Brasil 2024',
        game: 'Counter-Strike 2',
        organizerId: organizer.id,
        description: 'Campeonato nacional de CS2 com as melhores equipes do país.',
        format: 'SINGLE_ELIMINATION',
        maxParticipants: 16,
        registrationFee: 50,
        prizePool: 5000,
        rules: [
          'Times de 5 jogadores',
          'Modo Competitivo',
          'Mapas: Mirage, Inferno, Cache, Overpass',
          'Proibido uso de cheats'
        ],
        platforms: ['Steam'],
        startDate: new Date('2024-03-15T10:00:00Z'),
        endDate: new Date('2024-03-17T22:00:00Z'),
        registrationDeadline: new Date('2024-03-10T23:59:59Z'),
        status: 'REGISTRATION',
        prizeDistributions: {
          create: [
            { place: 1, percentage: 50 },
            { place: 2, percentage: 30 },
            { place: 3, percentage: 20 }
          ]
        }
      }
    });

    const tournament2 = await prisma.tournament.create({
      data: {
        name: 'Valorant Cup 2024',
        game: 'Valorant',
        organizerId: organizer.id,
        description: 'Torneio Valorant para jogadores intermediários e avançados.',
        format: 'DOUBLE_ELIMINATION',
        maxParticipants: 32,
        registrationFee: 30,
        prizePool: 3000,
        rules: [
          'Times de 5 jogadores',
          'Modo Padrão',
          'Agentes livres',
          'Respeito aos adversários'
        ],
        platforms: ['Riot Games'],
        startDate: new Date('2024-04-01T14:00:00Z'),
        endDate: new Date('2024-04-03T20:00:00Z'),
        registrationDeadline: new Date('2024-03-25T23:59:59Z'),
        status: 'UPCOMING',
        prizeDistributions: {
          create: [
            { place: 1, percentage: 60 },
            { place: 2, percentage: 25 },
            { place: 3, percentage: 15 }
          ]
        }
      }
    });

    // Registrar jogadores nos torneios
    await prisma.tournamentRegistration.create({
      data: {
        tournamentId: tournament1.id,
        participantId: player1.id,
        participantType: 'PLAYER'
      }
    });

    await prisma.tournamentRegistration.create({
      data: {
        tournamentId: tournament1.id,
        participantId: team.id,
        participantType: 'TEAM'
      }
    });

    console.log('✅ Seed concluído com sucesso!');
    console.log(`📊 Dados criados:`);
    console.log(`   - 1 Organizador: ${organizer.email}`);
    console.log(`   - 3 Jogadores: ${player1.email}, ${player2.email}, ${player3.email}`);
    console.log(`   - 1 Time: ${team.name}`);
    console.log(`   - 2 Torneios: ${tournament1.name}, ${tournament2.name}`);
    
    return {
      success: true,
      data: {
        organizer,
        players: [player1, player2, player3],
        team,
        tournaments: [tournament1, tournament2]
      }
    };

  } catch (error) {
    console.error('❌ Erro no seed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  } finally {
    await prisma.$disconnect();
  }
}

// Executar seed se este arquivo for chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDatabase().then(result => {
    if (result.success) {
      console.log('\n🎉 SEED EXECUTADO COM SUCESSO!');
      process.exit(0);
    } else {
      console.log('\n❌ ERRO NO SEED:', result.error);
      process.exit(1);
    }
  }).catch(console.error);
}
