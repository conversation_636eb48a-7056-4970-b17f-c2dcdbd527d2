import { PrismaClient } from '@prisma/client';

// Singleton pattern para o cliente Prisma no servidor
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

// Função para conectar ao banco
export async function connectToDatabase() {
  try {
    await prisma.$connect();
    console.log('✅ Conectado ao banco PostgreSQL');
  } catch (error) {
    console.error('❌ Erro ao conectar ao banco:', error);
    throw error;
  }
}

// Função para desconectar do banco
export async function disconnectFromDatabase() {
  try {
    await prisma.$disconnect();
    console.log('✅ Desconectado do banco PostgreSQL');
  } catch (error) {
    console.error('❌ Erro ao desconectar do banco:', error);
  }
}

// Conectar automaticamente quando o módulo for importado
connectToDatabase();
