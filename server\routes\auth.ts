import express from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { prisma } from '../lib/prisma';
import { authenticateToken, AuthRequest } from '../middleware/auth';

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'campmaker_jwt_secret_key_2024';

// POST /api/auth/register
router.post('/register', async (req, res) => {
  try {
    const { email, password, name, type } = req.body;

    // Validações
    if (!email || !password || !name || !type) {
      return res.status(400).json({
        success: false,
        error: 'Todos os campos são obrigatórios'
      });
    }

    if (!['player', 'organizer'].includes(type)) {
      return res.status(400).json({
        success: false,
        error: 'Tipo de usuário inválido'
      });
    }

    // Verificar se email já existe
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'Email já está em uso'
      });
    }

    // Hash da senha
    const hashedPassword = await bcrypt.hash(password, 10);

    // Criar usuário
    const newUser = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name,
        type: type === 'player' ? 'PLAYER' : 'ORGANIZER'
      }
    });

    // Se for jogador, criar perfil
    if (type === 'player') {
      await prisma.playerProfile.create({
        data: {
          userId: newUser.id,
          preferredGames: []
        }
      });
    }

    // Gerar token
    const token = jwt.sign(
      { userId: newUser.id, email: newUser.email, type: newUser.type },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.status(201).json({
      success: true,
      user: {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
        type: newUser.type.toLowerCase(),
        createdAt: newUser.createdAt.toISOString()
      },
      token
    });

  } catch (error) {
    console.error('Erro no registro:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

// POST /api/auth/login
router.post('/login', async (req, res) => {
  try {
    const { email, password, type } = req.body;

    // Validações
    if (!email || !password || !type) {
      return res.status(400).json({
        success: false,
        error: 'Email, senha e tipo são obrigatórios'
      });
    }

    // Buscar usuário
    const user = await prisma.user.findFirst({
      where: {
        email,
        type: type === 'player' ? 'PLAYER' : 'ORGANIZER'
      },
      include: {
        playerProfile: true
      }
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Credenciais inválidas'
      });
    }

    // Verificar senha
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        error: 'Credenciais inválidas'
      });
    }

    // Gerar token
    const token = jwt.sign(
      { userId: user.id, email: user.email, type: user.type },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        type: user.type.toLowerCase(),
        avatar: user.avatar,
        createdAt: user.createdAt.toISOString()
      },
      token
    });

  } catch (error) {
    console.error('Erro no login:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

// GET /api/auth/me
router.get('/me', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user!.id },
      include: {
        playerProfile: true
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'Usuário não encontrado'
      });
    }

    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        type: user.type.toLowerCase(),
        avatar: user.avatar,
        createdAt: user.createdAt.toISOString()
      }
    });

  } catch (error) {
    console.error('Erro ao buscar usuário:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

// POST /api/auth/logout
router.post('/logout', authenticateToken, (req, res) => {
  // Em uma implementação real, você poderia invalidar o token
  res.json({
    success: true,
    message: 'Logout realizado com sucesso'
  });
});

export { router as authRoutes };
