import React, { createContext, useContext, useState, useEffect } from 'react';
import { Tournament, Team, Notification, BracketMatch } from '../types';
import { apiService } from '../services/apiService';

export interface CreateTournamentData {
  name: string;
  game: string;
  description: string;
  format: 'single-elimination' | 'double-elimination' | 'groups' | 'swiss';
  maxParticipants: number;
  registrationFee: number;
  prizePool: number;
  prizeDistribution: { place: number; percentage: number }[];
  rules: string[];
  platforms: string[];
  startDate: string;
  endDate: string;
  registrationDeadline: string;
}

interface AppContextType {
  tournaments: Tournament[];
  teams: Team[];
  notifications: Notification[];
  loading: boolean;
  createTournament: (tournament: CreateTournamentData) => Promise<void>;
  updateTournament: (id: string, updates: Partial<Tournament>) => void;
  deleteTournament: (id: string) => void;
  registerForTournament: (tournamentId: string, participantId: string) => Promise<void>;
  generateBracket: (tournamentId: string) => void;
  updateMatchResult: (tournamentId: string, matchId: string, winner: string, score: { p1: number; p2: number }) => void;
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void;
  markNotificationRead: (id: string) => void;
  refreshTournaments: () => Promise<void>;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Mock data removido - agora usando TournamentServiceClient

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [tournaments, setTournaments] = useState<Tournament[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);

  // Carregar torneios da API
  const refreshTournaments = async () => {
    try {
      setLoading(true);
      const response = await apiService.getTournaments();
      if (response.success && response.tournaments) {
        setTournaments(response.tournaments);
      }
    } catch (error) {
      console.error('Erro ao carregar torneios:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refreshTournaments();
  }, []);

  const createTournament = async (tournamentData: CreateTournamentData) => {
    const response = await apiService.createTournament(tournamentData);

    if (response.success && response.tournament) {
      setTournaments(prev => [response.tournament, ...prev]);
    } else {
      throw new Error(response.error || 'Erro ao criar torneio');
    }
  };

  const updateTournament = (id: string, updates: Partial<Tournament>) => {
    setTournaments(prev => prev.map(t => t.id === id ? { ...t, ...updates } : t));
  };

  const deleteTournament = (id: string) => {
    setTournaments(prev => prev.filter(t => t.id !== id));
  };

  const registerForTournament = async (tournamentId: string, participantId: string) => {
    const response = await apiService.registerForTournament(tournamentId);

    if (response.success) {
      // Atualizar a lista local
      setTournaments(prev => prev.map(t =>
        t.id === tournamentId
          ? { ...t, participants: [...t.participants, participantId] }
          : t
      ));
    } else {
      throw new Error(response.error || 'Erro ao registrar no torneio');
    }
  };

  const generateBracket = (tournamentId: string) => {
    const tournament = tournaments.find(t => t.id === tournamentId);
    if (!tournament) return;

    const participants = tournament.participants;
    const bracket: BracketMatch[] = [];
    
    // Generate first round matches
    for (let i = 0; i < participants.length; i += 2) {
      if (i + 1 < participants.length) {
        bracket.push({
          id: `match-${i/2}`,
          round: 1,
          position: i/2,
          participant1: participants[i],
          participant2: participants[i + 1],
          status: 'pending'
        });
      }
    }

    updateTournament(tournamentId, { bracket, status: 'ongoing' });
  };

  const updateMatchResult = (tournamentId: string, matchId: string, winner: string, score: { p1: number; p2: number }) => {
    const tournament = tournaments.find(t => t.id === tournamentId);
    if (!tournament || !tournament.bracket) return;

    const updatedBracket = tournament.bracket.map(match => 
      match.id === matchId 
        ? { ...match, winner, score, status: 'completed' as const }
        : match
    );

    // Advance winner to next round (simplified logic)
    // In a real app, this would be more complex bracket management
    
    updateTournament(tournamentId, { bracket: updatedBracket });
  };

  const addNotification = (notificationData: Omit<Notification, 'id' | 'createdAt'>) => {
    const newNotification: Notification = {
      ...notificationData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString()
    };
    setNotifications(prev => [newNotification, ...prev]);
  };

  const markNotificationRead = (id: string) => {
    setNotifications(prev => prev.map(n => n.id === id ? { ...n, read: true } : n));
  };

  return (
    <AppContext.Provider value={{
      tournaments,
      teams,
      notifications,
      loading,
      createTournament,
      updateTournament,
      deleteTournament,
      registerForTournament,
      generateBracket,
      updateMatchResult,
      addNotification,
      markNotificationRead,
      refreshTournaments
    }}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}