import React, { createContext, useContext, useState, useEffect } from 'react';
import { Tournament, Team, Notification, BracketMatch } from '../types';

interface AppContextType {
  tournaments: Tournament[];
  teams: Team[];
  notifications: Notification[];
  createTournament: (tournament: Omit<Tournament, 'id' | 'createdAt' | 'participants' | 'status'>) => void;
  updateTournament: (id: string, updates: Partial<Tournament>) => void;
  deleteTournament: (id: string) => void;
  registerForTournament: (tournamentId: string, participantId: string) => void;
  generateBracket: (tournamentId: string) => void;
  updateMatchResult: (tournamentId: string, matchId: string, winner: string, score: { p1: number; p2: number }) => void;
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void;
  markNotificationRead: (id: string) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Mock data
const mockTournaments: Tournament[] = [
  {
    id: '1',
    name: 'Liga CS2 Brasil',
    game: 'Counter-Strike 2',
    organizerId: '1',
    description: 'Campeonato nacional de CS2 com as melhores equipes do país.',
    format: 'single-elimination',
    maxParticipants: 16,
    registrationFee: 50,
    prizePool: 5000,
    prizeDistribution: [
      { place: 1, percentage: 50 },
      { place: 2, percentage: 30 },
      { place: 3, percentage: 20 }
    ],
    rules: [
      'Times de 5 jogadores',
      'Modo Competitivo',
      'Mapas: Mirage, Inferno, Cache, Overpass'
    ],
    platforms: ['Steam'],
    startDate: '2024-02-15T10:00:00Z',
    endDate: '2024-02-18T22:00:00Z',
    registrationDeadline: '2024-02-10T23:59:59Z',
    status: 'registration',
    participants: ['team1', 'team2'],
    createdAt: '2024-01-15T00:00:00Z'
  },
  {
    id: '2',
    name: 'Valorant Cup',
    game: 'Valorant',
    organizerId: '1',
    description: 'Torneio Valorant para jogadores intermediários e avançados.',
    format: 'double-elimination',
    maxParticipants: 32,
    registrationFee: 30,
    prizePool: 3000,
    prizeDistribution: [
      { place: 1, percentage: 60 },
      { place: 2, percentage: 25 },
      { place: 3, percentage: 15 }
    ],
    rules: [
      'Times de 5 jogadores',
      'Modo Padrão',
      'Agentes livres'
    ],
    platforms: ['Riot Games'],
    startDate: '2024-02-20T14:00:00Z',
    endDate: '2024-02-23T20:00:00Z',
    registrationDeadline: '2024-02-15T23:59:59Z',
    status: 'upcoming',
    participants: [],
    createdAt: '2024-01-20T00:00:00Z'
  }
];

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [tournaments, setTournaments] = useState<Tournament[]>(mockTournaments);
  const [teams, setTeams] = useState<Team[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const createTournament = (tournamentData: Omit<Tournament, 'id' | 'createdAt' | 'participants' | 'status'>) => {
    const newTournament: Tournament = {
      ...tournamentData,
      id: Date.now().toString(),
      participants: [],
      status: 'upcoming',
      createdAt: new Date().toISOString()
    };
    setTournaments(prev => [...prev, newTournament]);
  };

  const updateTournament = (id: string, updates: Partial<Tournament>) => {
    setTournaments(prev => prev.map(t => t.id === id ? { ...t, ...updates } : t));
  };

  const deleteTournament = (id: string) => {
    setTournaments(prev => prev.filter(t => t.id !== id));
  };

  const registerForTournament = (tournamentId: string, participantId: string) => {
    setTournaments(prev => prev.map(t => 
      t.id === tournamentId 
        ? { ...t, participants: [...t.participants, participantId] }
        : t
    ));
  };

  const generateBracket = (tournamentId: string) => {
    const tournament = tournaments.find(t => t.id === tournamentId);
    if (!tournament) return;

    const participants = tournament.participants;
    const bracket: BracketMatch[] = [];
    
    // Generate first round matches
    for (let i = 0; i < participants.length; i += 2) {
      if (i + 1 < participants.length) {
        bracket.push({
          id: `match-${i/2}`,
          round: 1,
          position: i/2,
          participant1: participants[i],
          participant2: participants[i + 1],
          status: 'pending'
        });
      }
    }

    updateTournament(tournamentId, { bracket, status: 'ongoing' });
  };

  const updateMatchResult = (tournamentId: string, matchId: string, winner: string, score: { p1: number; p2: number }) => {
    const tournament = tournaments.find(t => t.id === tournamentId);
    if (!tournament || !tournament.bracket) return;

    const updatedBracket = tournament.bracket.map(match => 
      match.id === matchId 
        ? { ...match, winner, score, status: 'completed' as const }
        : match
    );

    // Advance winner to next round (simplified logic)
    // In a real app, this would be more complex bracket management
    
    updateTournament(tournamentId, { bracket: updatedBracket });
  };

  const addNotification = (notificationData: Omit<Notification, 'id' | 'createdAt'>) => {
    const newNotification: Notification = {
      ...notificationData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString()
    };
    setNotifications(prev => [newNotification, ...prev]);
  };

  const markNotificationRead = (id: string) => {
    setNotifications(prev => prev.map(n => n.id === id ? { ...n, read: true } : n));
  };

  return (
    <AppContext.Provider value={{
      tournaments,
      teams,
      notifications,
      createTournament,
      updateTournament,
      deleteTournament,
      registerForTournament,
      generateBracket,
      updateMatchResult,
      addNotification,
      markNotificationRead
    }}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}